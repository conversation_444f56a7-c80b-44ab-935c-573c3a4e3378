import DksConfigItem from './DksConfigItem';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useEffect, useState } from 'react';
import { Modal, Button, Slider ,InputNumber} from 'antd';
import Keymap from '../../Keymap/Keymap';
import { changeToHighLowHex, findCodeByKey, findNameByCode, parseHex, binaryToHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
import processString from '../../Keyboard/processString' ;

const DksConfig = ({currentStep}) => {
  const { data } = useKeyboard();
  const { advancedKey, setAdvancedKey } = useHandleDevice();
  const [activeKey, setActiveKey] = useState('keycap1');
  const currentKey = data.keycaps[data.currentLayer]?.[`${advancedKey.dks.list[advancedKey.selectedIndex]?.keycap_row}-${advancedKey.dks.list[advancedKey.selectedIndex]?.keycap_col}`];
  const newList = [...advancedKey.dks.list];
  const currentItem = newList[advancedKey.selectedIndex]
  const [open, setOpen] = useState(false);
  const [openSlider, setOpenSlider] = useState(false);
  const [range, setRange] = useState(currentItem ? [(parseHex(currentItem.press_trigger_point) / 100).toFixed(2), (parseHex(currentItem.release_trigger_point) / 100).toFixed(2)] : [0, 0]);
  const { t } = useTranslation();

  const handleKeyClick = (key) => {
    setActiveKey(key);
    setOpen(true)
  }

  useEffect(() => {
    if (currentItem) {
      currentItem.keycap_row = data.currentSelectedKeycaps[0]?.row || 0;
      currentItem.keycap_col = data.currentSelectedKeycaps[0]?.column || 0;
    }
    setAdvancedKey(prev => ({
      ...prev,
      dks: {
        ...prev.dks,
        list: newList
      }
    }))
  }, [data.currentSelectedKeycaps]);

  const handleKeycapClick = (keycapName) => {
    if (activeKey === 'keycap1') {
      currentItem.keycap1_code = findCodeByKey(keycapName)
    } else if (activeKey === 'keycap2') {
      currentItem.keycap2_code = findCodeByKey(keycapName)
    } else if (activeKey === 'keycap3') {
      currentItem.keycap3_code = findCodeByKey(keycapName)
    } else if (activeKey === 'keycap4') {
      currentItem.keycap4_code = findCodeByKey(keycapName)
    }
    setAdvancedKey(prev => ({
      ...prev,
      dks: {
        ...prev.dks,
        list: newList
      }
    }))
    setOpen(false)
  };

  const handleTriggerPointClick = (type, value) => {
    setOpenSlider(true)
  }

  const handlePointDataChange = (pointData, keyIndex) => {
    // Update the corresponding keycap config based on keyIndex
    if (currentItem) {
      switch(keyIndex) {
        case 1:
          currentItem.keycap1_config = binaryToHex(pointData);
          break;
        case 2:
          currentItem.keycap2_config = binaryToHex(pointData);
          break;
        case 3:
          currentItem.keycap3_config = binaryToHex(pointData);
          break;
        case 4:
          currentItem.keycap4_config = binaryToHex(pointData);
          break;
      }

      setAdvancedKey(prev => ({
        ...prev,
        dks: {
          ...prev.dks,
          list: newList
        }
      }));
    }
  };

  const handleSliderChange = (value, prevValue) => {
    const [min, max] = value;
    if (min >= max) return; // 如果越界，忽略这次操作
    setRange(value);
    if (min < max) {
      currentItem.press_trigger_point = changeToHighLowHex(min * 100)
      currentItem.release_trigger_point = changeToHighLowHex(max * 100)
    } else if (min > max) {
      currentItem.press_trigger_point = changeToHighLowHex(max * 100)
      currentItem.release_trigger_point = changeToHighLowHex(min * 100)
    }

    setAdvancedKey(prev => ({
      ...prev,
      dks: {
        ...prev.dks,
        list: newList
      }
    }))
  }

  return (<>
 {
    currentStep === 2 && <>
    <div className="dks-config">
      <div className="rs-config-content">
        <div className='advanced_keyrs_title'><span>{t('advanced_key.dks.advanced_keyrs_title')}</span></div>
        <div className='advanced_keyrs_content'><span>{t('advanced_key.dks.advanced_keyrs_content')}</span></div>
      </div>
      <div className="rs-config-item">
        <div className={`rs-config-key-container active`}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
                <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${currentKey?.label  ?   processString(currentKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
            {
              currentKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="#196BE5"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
            <span>{ t('advanced_key.dks.choose_key_1')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
       </>
    }
    {
    currentStep === 3 && <>
    <div className="dks-config">
      <div className="rs-config-content">
        <div className='advanced_keyrs_content'><span>{t('advanced_key.dks.advanced_modes_content')}</span></div>
      </div>
      <div className='dks-config-items'>
        <Modal
          centered
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
          width={1450}
          className={'keybordmodal'}
          footer={null}

        >
          <Keymap handleKeycapClick={handleKeycapClick} />
        </Modal>
        <Modal
          title=""
          centered
          open={openSlider}
          onOk={() => setOpenSlider(false)}
          onCancel={() => setOpenSlider(false)}
          footer={null}
        >
          <div className='d-flex align-items-center justify-content-center'>
            <Slider
              vertical
              range
              reverse
              value={range}
              onChange={handleSliderChange}
              min={0.01}
              max={4}
              step={0.01}
              style={{height: '150px'}}
            />
            <div style={{marginLeft: '2em', width: '300px'}}>
              {t('advanced_key.dks.first_and_fourth_dks_action_execution_point')}: {(parseHex(currentItem.press_trigger_point) / 100).toFixed(2)}mm
              <br/>
              {t('advanced_key.dks.second_and_third_dks_action_execution_point')}: {(parseHex(currentItem.release_trigger_point) / 100).toFixed(2)}mm
            </div>
          </div>
        </Modal>
        <div className='dks-config-items-tops'>
            <div className='dks-config-items-container-left'>
            </div>
            <div className='dks-config-items-container'>
             <div className='dks-config-item-container'>
              <div className='dks-config-top-item'>
                <div className='dks-config-top-item-title'>
                  <span>{t('advanced_key.dks.press_start')}</span>
                </div>
                <Button type="link" onClick={() => handleTriggerPointClick('press_trigger_point')}>
                  <div className='dks-config-top-item-text'>
                    <span className='dks-config-top-item-text-number'>{(parseHex(currentItem.press_trigger_point) / 100).toFixed(2)} </span>
                    <span>mm</span>
                  </div>
                </Button>
              </div>
              <div className='dks-config-top-item'>
                <div className='dks-config-top-item-title'>
                  <span>{t('advanced_key.dks.press_bottom')}</span>
                </div>
                <Button type="link" onClick={() => handleTriggerPointClick('release_trigger_point')}>
                  <div className='dks-config-top-item-text'>
                    <span className='dks-config-top-item-text-number'>{(parseHex(currentItem.release_trigger_point) / 100).toFixed(2)} </span>
                    <span>mm</span>
                  </div>
                </Button>
              </div>
              <div className='dks-config-top-item'>
                <div className='dks-config-top-item-title'>
                  <span>{t('advanced_key.dks.bottom_release')}</span>
                </div>
                <Button type="link" onClick={() => handleTriggerPointClick('release_trigger_point')}>
                  <div className='dks-config-top-item-text'>
                    <span className='dks-config-top-item-text-number'>{(parseHex(currentItem.release_trigger_point) / 100).toFixed(2)} </span>
                    <span>mm</span>
                  </div>
                </Button>
              </div>
              <div className='dks-config-top-item'>
                <div className='dks-config-top-item-title'>
                  <span>{t('advanced_key.dks.release_end')}</span>
                </div>
                <Button type="link" onClick={() => handleTriggerPointClick('press_trigger_point')}>
                  <div className='dks-config-top-item-text'>
                    <span className='dks-config-top-item-text-number'>{(parseHex(currentItem.press_trigger_point) / 100).toFixed(2)} </span>
                    <span>mm</span>
                  </div>
                </Button>
              </div>
             </div>
            </div>
        </div>
        <div className='dks-config-items-main'>
          <div className='dks-config-items-main-items'>
            <div className='dks-config-items-main-item'>
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_1')}</span></div>
               <div className={`dks-config-items-main-item-key-container ${currentItem.keycap1_code && 'active'} `} onClick={() => handleKeyClick('keycap1')}>
                   {currentItem.keycap1_code ? findNameByCode(currentItem.keycap1_code) : <svg aria-hidden="true"  width="36"  height="36">
                  <use href= '#icon-evenodd'/>
                </svg>
                }
               </div>
            </div>
            <div className='dks-config-items-main-item'>
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_2')}</span></div>
               <div  className={`dks-config-items-main-item-key-container ${currentItem.keycap2_code && 'active'} `}  onClick={() => handleKeyClick('keycap2')}>
                   {currentItem.keycap2_code ? findNameByCode(currentItem.keycap2_code) :  <svg aria-hidden="true"  width="36"  height="36">
                  <use href= '#icon-evenodd'/>
                </svg>}
               </div>
            </div>
            <div className='dks-config-items-main-item'>
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_3')}</span></div>
               <div  className={`dks-config-items-main-item-key-container ${currentItem.keycap3_code && 'active'} `}  onClick={() => handleKeyClick('keycap3')}>
                   {currentItem.keycap3_code ? findNameByCode(currentItem.keycap3_code) :  <svg aria-hidden="true"  width="36"  height="36">
                  <use href= '#icon-evenodd'/>
                </svg>}
               </div>
            </div>
            <div className='dks-config-items-main-item'>
               <div className='dks-config-items-main-item-text'><span>{t('advanced_key.dks.key_4')}</span></div>
               <div  className={`dks-config-items-main-item-key-container ${currentItem.keycap4_code && 'active'} `}  onClick={() => handleKeyClick('keycap4')}>
                   {currentItem.keycap4_code ? findNameByCode(currentItem.keycap4_code) :  <svg aria-hidden="true"  width="36"  height="36">
                  <use href= '#icon-evenodd'/>
                </svg>}
               </div>
            </div>
          </div>
          <div className='dks-config-items-main-container'>
            <div className='dks-config-items-main-container-items'>
              <div className='dks-config-items-main-container-item'>
              <DksConfigItem
              getFinalPointData={handlePointDataChange}
              keycap_config={currentItem.keycap1_config}
              keyIndex={1}
            />
              </div>
              <div className='dks-config-items-main-container-item'>
              <DksConfigItem
              getFinalPointData={handlePointDataChange}
              keyIndex={2}
              keycap_config={currentItem.keycap2_config}
            />
              </div>
              <div className='dks-config-items-main-container-item'>
              <DksConfigItem
              getFinalPointData={handlePointDataChange}
              keyIndex={3}
              keycap_config={currentItem.keycap3_config}
            />
              </div>
              <div className='dks-config-items-main-container-item'>
              <DksConfigItem
              getFinalPointData={handlePointDataChange}
              keyIndex={4}
              keycap_config={currentItem.keycap4_config}
            />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
      </>
    }
    </>)
}

export default DksConfig;