import { CloseOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import { findNameByCode } from '../../../utils/hidUtils';
import DksIcon from './DksIcon';

const DksItem = ({ item, isActive, onSelect, onDelete }) => {
  const { data } = useKeyboard();
  const selectedKeycap = data.keycaps[data.currentLayer]?.[`${item.keycap_row}-${item.keycap_col}`];
  const keycap1 = findNameByCode(`${item.keycap1_code}`);
  const keycap2 = findNameByCode(`${item.keycap2_code}`);
  const keycap3 = findNameByCode(`${item.keycap3_code}`);
  const keycap4 = findNameByCode(`${item.keycap4_code}`);

  return (
    <div className="advanced-key-item-wrapper" onClick={onSelect}>
      <div className={`d-flex justify-content-between align-items-center advanced-key-item ${isActive ? 'active' : ''}`}>
        <div className="d-flex align-items-center">
          <div className="d-flex align-items-center" style={{width: '80px', gap: '8px'}}>
            <div className="keycap1 keycap-item">{selectedKeycap?.label || ''}</div>
          </div>
          <div className="divider-line"></div>
          <div className="keycap1 keycap-item">{keycap1 || ''}</div>
          <div className="keycap2 keycap-item" style={{marginLeft: '0.5em'}}>{keycap2 || ''}</div>
          <div className="keycap3 keycap-item" style={{marginLeft: '0.5em'}}>{keycap3 || ''}</div>
          <div className="keycap4 keycap-item" style={{marginLeft: '0.5em'}}>{keycap4 || ''}</div>
        </div>
        <div className="d-flex align-items-center">
          <div className="icon">
            <DksIcon color={isActive ? "#1668dc" : "#EFF0F5"} fillOpacity={isActive ? "1" : "0.45"} />
          </div>
            <div style={{width: '36px'}}>
              <Button type="text" icon={<CloseOutlined />} className="delete-button" onClick={onDelete} />
            </div>
        </div>
      </div>
    </div>
  );
};

export default DksItem;