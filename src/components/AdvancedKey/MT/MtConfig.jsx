import { <PERSON><PERSON><PERSON>, Mo<PERSON> } from 'antd';
import { useState, useEffect } from 'react';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import Keymap from '../../Keymap';
import { findCodeByKey, findNameByCode, parseHex, changeToHighLowHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
const MtConfig = () => {
  const [activeKey, setActiveKey] = useState('current');
  const { advancedKey, setAdvancedKey, addToQueue } = useHandleDevice();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const [open, setOpen] = useState(false);
  const currentKey = data.keycaps[data.currentLayer]?.[`${advancedKey.mt.list[advancedKey.selectedIndex]?.keycap_row}-${advancedKey.mt.list[advancedKey.selectedIndex]?.keycap_col}`];
  const newList = [...advancedKey.mt.list];
  const currentItem = newList[advancedKey.selectedIndex];
  const { t } = useTranslation();
  const handleKeyClick = (key) => {
    setActiveKey(key);
    if (key === 'current') {

    } else if (key === 'hold') {
      setOpen(true)
    } else if (key === 'click') {
      setOpen(true)
    }
  }

  useEffect(() => {
    if (currentItem) {
      currentItem.keycap_row = data.currentSelectedKeycaps[0]?.row || 0;
      currentItem.keycap_col = data.currentSelectedKeycaps[0]?.column || 0;
    }
    setAdvancedKey(prev => ({
      ...prev,
      mt: {
        ...prev.mt,
        list: newList
      }
    }))
  }, [data.currentSelectedKeycaps]);

  const handleKeycapClick = (keycapName) => {
    if (activeKey === 'click') {
      currentItem.short_press_code = findCodeByKey(keycapName)
    } else if (activeKey === 'hold') {
      currentItem.long_press_code = findCodeByKey(keycapName)
    }
    setAdvancedKey(prev => ({
      ...prev,
      mt: {
        ...prev.mt,
        list: newList
      }
    }))
    setOpen(false)
  };

  return (
    currentItem ? <div>
      <Modal
        // title={t('advanced_key.mt.choose_keyboard')}
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        width={1450}
        className={'keybordmodal'}
      >
        <Keymap handleKeycapClick={handleKeycapClick} />
      </Modal>
      <div style={{marginBottom: '2em'}}>{t('advanced_key.mt.mt_config')}</div>
      <div className="d-flex align-items-center justify-content-center">
        <div className="d-flex align-items-center" style={{flexDirection: 'column', marginRight: '2em'}}>
          <div>{t('advanced_key.mt.current_key')}</div>
          <div
            className={`key-container ${activeKey === 'current' ? 'active' : ''}`}
            onClick={() => handleKeyClick('current')}
          >
            {currentKey?.label || t('advanced_key.mt.choose_keyboard')}
          </div>
        </div>
        <div className="d-flex align-items-center" style={{flexDirection: 'column', marginRight: '2em'}}>
          <div>{t('advanced_key.mt.hold_key')}</div>
          <div
            className={`key-container ${activeKey === 'hold' ? 'active' : ''}`}
            onClick={() => handleKeyClick('hold')}
          >
            {currentItem.long_press_code ? findNameByCode(currentItem.long_press_code) : t('advanced_key.mt.choose_key')}
          </div>
        </div>
        <div className="d-flex align-items-center" style={{flexDirection: 'column', marginRight: '2em'}}>
          <div>{t('advanced_key.mt.single_click')}</div>
          <div
            className={`key-container ${activeKey === 'click' ? 'active' : ''}`}
            onClick={() => handleKeyClick('click')}
          >
            {currentItem.short_press_code ? findNameByCode(currentItem.short_press_code) : t('advanced_key.mt.choose_key')}
          </div>
        </div>
      </div>
      <div style={{marginTop: '5em'}}>{t('advanced_key.mt.hold_time_tip')}</div>
      <div className="d-flex align-items-center">
        <Slider defaultValue={parseHex(currentItem.long_press_time)} min={100} max={500} style={{width: '50%'}} onChange={(value) => {
          currentItem.long_press_time = changeToHighLowHex(value)
          setAdvancedKey(prev => ({
            ...prev,
            mt: {
              ...prev.mt,
              list: newList
            }
          }))
        }} />
        <div style={{marginLeft: '1em'}}>{parseHex(currentItem.long_press_time)}ms</div>
      </div>
    </div> : <div></div>
  )
}

export default MtConfig;