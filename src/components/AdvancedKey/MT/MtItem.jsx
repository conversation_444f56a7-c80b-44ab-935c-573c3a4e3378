import { CloseOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import { findNameByCode } from '../../../utils/hidUtils';
import MtIcon from './MtIcon';
const MtItem = ({ item, isActive, onSelect, onDelete }) => {
  const { data } = useKeyboard();
  const selectedKeycap = data.keycaps[data.currentLayer]?.[`${item.keycap_row}-${item.keycap_col}`];
  const shortPressKeycap = findNameByCode(`${item.short_press_code}`);
  const longPressKeycap = findNameByCode(`${item.long_press_code}`);

  return (
    <div className="advanced-key-item-wrapper" onClick={onSelect}>
      <div className={`d-flex justify-content-between align-items-center advanced-key-item ${isActive ? 'active' : ''}`}>
        <div className="d-flex align-items-center">
          <div className="d-flex align-items-center" style={{width: '80px', gap: '8px'}}>
            <div className="keycap1 keycap-item">{selectedKeycap?.label || ''}</div>
          </div>
          <div className="divider-line"></div>
          <div className="long-press-keycap keycap-item">{longPressKeycap || ''}</div>
          <div className="short-press-keycap keycap-item" style={{marginLeft: '0.5em'}}>{shortPressKeycap || ''}</div>
        </div>
        <div className="d-flex align-items-center">
          <div className="icon">
            <MtIcon color={isActive ? "#1668dc" : "#EFF0F5"} fillOpacity={isActive ? "1" : "0.45"} />
          </div>
          <div style={{width: '36px'}}>
            <Button type="text" icon={<CloseOutlined />} className="delete-button" onClick={onDelete} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MtItem;