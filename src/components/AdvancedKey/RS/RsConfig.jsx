import React from 'react';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import { useState, useEffect } from 'react';
import { Button } from 'antd';
import { parseHex, changeToHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
import processString from '../../Keyboard/processString' ;

const RsConfig = ({
  firstKeyRow,
  firstKeyCol,
  secondKeyRow,
  secondKeyCol,
  selectedMode,
  handleSave,
}) => {
  const { t } = useTranslation();
  const { advancedKey, setAdvancedKey, addToQueue } = useHandleDevice();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const firstKey = data.keycaps[data.currentLayer]?.[`${firstKeyRow}-${firstKeyCol}`];
  const secondKey = data.keycaps[data.currentLayer]?.[`${secondKeyRow}-${secondKeyCol}`];
  const [activeKeyInput, setActiveKeyInput] = useState(1);
  let firstKeyInput = {
    row: firstKeyRow || 0,
    col: firstKeyCol || 0
  };
  let secondKeyInput = {
    row: secondKeyRow || 0,
    col: secondKeyCol || 0
  };

  const SelectedKeycapsSync = () => {

    const newList = [...advancedKey.rs.list];
    const currentItem = newList[advancedKey.selectedIndex];

    if (currentItem) {
      if (data.currentSelectedKeycaps.length === 1) {
        if (secondKeyInput.row === data.currentSelectedKeycaps[0].row && secondKeyInput.col === data.currentSelectedKeycaps[0].column) {
          firstKeyInput = { row: 0, col: 0 };
          secondKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };

        } else {
          firstKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
          secondKeyInput = { row: 0, col: 0 };
        }
        // Compare with firstKeyInput and secondKeyInput to determine which one was deselected
        if (firstKeyInput.row === data.currentSelectedKeycaps[0].row && firstKeyInput.col === data.currentSelectedKeycaps[0].column) {
          // First key remains selected, reset second key
          firstKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
          secondKeyInput = { row: 0, col: 0 };
          currentItem.keycap1_row = data.currentSelectedKeycaps[0].row;
          currentItem.keycap1_col = data.currentSelectedKeycaps[0].column;
          currentItem.keycap2_row = 0;
          currentItem.keycap2_col = 0;
          setActiveKeyInput(2)
        } else if (secondKeyInput.row === data.currentSelectedKeycaps[0].row && secondKeyInput.col === data.currentSelectedKeycaps[0].column) {
          // Second key remains selected, reset first key
          firstKeyInput = { row: 0, col: 0 };
          secondKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
          currentItem.keycap1_row = 0;
          currentItem.keycap1_col = 0;
          currentItem.keycap2_row = data.currentSelectedKeycaps[0].row;
          currentItem.keycap2_col = data.currentSelectedKeycaps[0].column;
          setActiveKeyInput(1)
        }
      } else if (data.currentSelectedKeycaps.length === 2) {
        if (data.currentSelectedKeycaps[0].label != undefined && data.currentSelectedKeycaps[1].label != undefined) {
          if (secondKeyInput.row === data.currentSelectedKeycaps[0].row && secondKeyInput.col === data.currentSelectedKeycaps[0].column) {
            firstKeyInput = { row: data.currentSelectedKeycaps[1].row, col: data.currentSelectedKeycaps[1].column };
            secondKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
            currentItem.keycap1_row = data.currentSelectedKeycaps[1].row;
            currentItem.keycap1_col = data.currentSelectedKeycaps[1].column;
            currentItem.keycap2_row = data.currentSelectedKeycaps[0].row;
            currentItem.keycap2_col = data.currentSelectedKeycaps[0].column;
          } else {
            firstKeyInput = { row: data.currentSelectedKeycaps[0].row, col: data.currentSelectedKeycaps[0].column };
            secondKeyInput = { row: data.currentSelectedKeycaps[1].row, col: data.currentSelectedKeycaps[1].column };
            currentItem.keycap1_row = data.currentSelectedKeycaps[0].row;
            currentItem.keycap1_col = data.currentSelectedKeycaps[0].column;
            currentItem.keycap2_row = data.currentSelectedKeycaps[1].row;
            currentItem.keycap2_col = data.currentSelectedKeycaps[1].column;
          }
          setActiveKeyInput(2)
        } else {
          setCurrentSelectedKeycaps([])
        }
      } else {
        firstKeyInput = { row: 0, col: 0 };
        secondKeyInput = { row: 0, col: 0 };
        currentItem.keycap1_row = 0;
        currentItem.keycap1_col = 0;
        currentItem.keycap2_row = 0;
        currentItem.keycap2_col = 0;
        setActiveKeyInput(1)
      }
    }

    setAdvancedKey({
      ...advancedKey,
      rs: {
        ...advancedKey.rs,
        list: newList
      }
    });
  }

  const rsKeyClick = (keyNumber) => {

    if (keyNumber === 1) {
      setActiveKeyInput(1)
      if (data.currentSelectedKeycaps.length === 1) {

        setCurrentSelectedKeycaps([])
      } else {
        setCurrentSelectedKeycaps([{ row: secondKeyInput.row, column: secondKeyInput.col, label: secondKey?.label }])
      }

    } else if (keyNumber === 2) {
      setActiveKeyInput(2)
      if (data.currentSelectedKeycaps.length === 1) {
        setCurrentSelectedKeycaps([])
      } else {
        setCurrentSelectedKeycaps([{ row: firstKeyInput.row, column: firstKeyInput.col, label: firstKey?.label }])
      }
    }
  };

  useEffect(() => {
    SelectedKeycapsSync()
  }, [data.currentSelectedKeycaps]);



  return (
    <div className="rs-config">
      <div className="rs-config-content">
        <div className='advanced_keyrs_title'><span>{t('advanced_key.rs.advanced_keyrs_title')}</span></div>
        <div className='advanced_keyrs_content'><span>{t('advanced_key.rs.advanced_keyrs_content')}</span></div>
      </div>
      <div className="rs-config-item">
        <div className={`rs-config-key-container ${activeKeyInput === 1 ? 'active' : ''}`}  onClick={() => rsKeyClick(1)}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
                <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${firstKey?.label  ?   processString(firstKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
            {
              firstKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="#196BE5"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
            <span>{firstKey?.label || t('advanced_key.socd.choose_key_1')}</span>
            </div>
          </div>
        </div>
        <div className={`rs-config-key-container ${activeKeyInput === 2 ? 'active' : ''}`} onClick={() => rsKeyClick(2)}>
          <div className="rs-config-key-choose">
             <div className="rs-config-key-choose-icon">
               <svg aria-hidden="true"  width="48"  height="48">
                  <use href= {`#icon-${secondKey?.label  ?   processString(secondKey?.label,'picture') : 'evenodd'}`}/>
                </svg>
             </div>
          </div>
          <div className="rs-config-key-change">
          {
              secondKey?.label && <div className='rs-config-key-change-check' >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8Z" fill="#196BE5"/>
                  <path d="M5.40078 11.1422L11.7428 4.80021L12.7998 5.85721L6.45778 12.1992L5.40078 11.1422Z" fill="white"/>
                  <path d="M4.20781 7.78125L7.31062 10.8841L6.25362 11.941L3.15082 8.83825L4.20781 7.78125Z" fill="white"/>
              </svg>
            </div>
            }
            <div className="rs-config-key-change-text">
               <span>{secondKey?.label || t('advanced_key.socd.choose_key_2')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RsConfig;