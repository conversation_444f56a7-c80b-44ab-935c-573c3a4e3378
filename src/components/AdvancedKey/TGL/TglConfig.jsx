import { <PERSON><PERSON><PERSON>, Mo<PERSON> } from 'antd';
import { useState, useEffect } from 'react';
import { useHandleDevice } from '../../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import Keymap from '../../Keymap';
import { findCodeByKey, findNameByCode, parseHex, changeToHighLowHex } from '../../../utils/hidUtils';
import { useTranslation } from 'react-i18next';
const TglConfig = () => {
  const [activeKey, setActiveKey] = useState('current');
  const { advancedKey, setAdvancedKey, addToQueue } = useHandleDevice();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const [open, setOpen] = useState(false);
  const currentKey = data.keycaps[data.currentLayer]?.[`${advancedKey.tgl.list[advancedKey.selectedIndex]?.keycap_row}-${advancedKey.tgl.list[advancedKey.selectedIndex]?.keycap_col}`];
  const newList = [...advancedKey.tgl.list];
  const currentItem = newList[advancedKey.selectedIndex];
  const { t } = useTranslation();
  const handleKeyClick = (key) => {
    setActiveKey(key);
    if (key === 'hold') {
      setOpen(true)
    }
  }

  useEffect(() => {
    if (currentItem) {
      currentItem.keycap_row = data.currentSelectedKeycaps[0]?.row || 0;
      currentItem.keycap_col = data.currentSelectedKeycaps[0]?.column || 0;
    }
    setAdvancedKey(prev => ({
      ...prev,
      tgl: {
        ...prev.tgl,
        list: newList
      }
    }))
  }, [data.currentSelectedKeycaps]);

  const handleKeycapClick = (keycapName) => {
    currentItem.keycode = findCodeByKey(keycapName)
    setAdvancedKey(prev => ({
      ...prev,
      tgl: {
        ...prev.tgl,
        list: newList
      }
    }))
    setOpen(false)
  };

  return (
    currentItem ? <div>
      <Modal
        // title={t('advanced_key.mt.choose_keyboard')}
        centered
        open={open}
        onOk={() => setOpen(false)}
        className={'keybordmodal'}
        onCancel={() => setOpen(false)}
        width={1450}
      >
        <Keymap handleKeycapClick={handleKeycapClick} />
      </Modal>
      <div style={{marginBottom: '2em'}}>{t('advanced_key.tgl.tgl_config')}</div>
      <div className="d-flex align-items-center justify-content-center">
        <div className="d-flex align-items-center" style={{flexDirection: 'column', marginRight: '2em'}}>
          <div>{t('advanced_key.mt.current_key')}</div>
          <div
            className={`key-container ${activeKey === 'current' ? 'active' : ''}`}
            onClick={() => handleKeyClick('current')}
          >
            {currentKey?.label || t('advanced_key.mt.choose_keyboard')}
          </div>
        </div>
        <div className="d-flex align-items-center" style={{flexDirection: 'column', marginRight: '2em'}}>
          <div>{t('advanced_key.mt.hold_key')}</div>
          <div
            className={`key-container ${activeKey === 'hold' ? 'active' : ''}`}
            onClick={() => handleKeyClick('hold')}
          >
            {currentItem.keycode ? findNameByCode(currentItem.keycode) : t('advanced_key.mt.choose_key')}
          </div>
        </div>
      </div>
    </div> : <div></div>
  )
}

export default TglConfig;