import React from 'react';
import { Tag, Spin } from 'antd';
import { useHandleDevice } from './HandleDeviceContext';
import { useTranslation } from 'react-i18next';
const HIDDevice = () => {
  const { handleOpenDevice, device } = useHandleDevice();
  const { t } = useTranslation();
  return (
    <>
      <div className="">
        {!device && <div id="device-connect">
          <Tag bordered={false} color="#1668dc" style={{cursor: "pointer"}} onClick={handleOpenDevice}>{t('settings.version_update.connect_device')}</Tag>
        </div>}
        {device && <div>
          <Spin />
        </div>}
      </div>
    </>
  );
};

export default HIDDevice;