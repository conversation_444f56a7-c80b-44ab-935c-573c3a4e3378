import { getPositionByNumber, findNameByCode } from "../../utils/hidUtils";
const InitKeyboardMapping = (hexArray, setCurrentLayer, updateKeycap, deviceProductId) => {
  const key_codes = [
    [hexArray[4], hexArray[5]],
    [hexArray[6], hexArray[7]],
    [hexArray[8], hexArray[9]],
    [hexArray[10], hexArray[11]],
    [hexArray[12], hexArray[13]],
    [hexArray[14], hexArray[15]],
    [hexArray[16], hexArray[17]],
    [hexArray[18], hexArray[19]],
    [hexArray[20], hexArray[21]],
    [hexArray[22], hexArray[23]],
    [hexArray[24], hexArray[25]],
    [hexArray[26], hexArray[27]],
    [hexArray[28], hexArray[29]],
    [hexArray[30], hexArray[31]]
  ];

  if (deviceProductId === 25344) {
    const start_position = parseInt(`${hexArray[1]}${hexArray[2]}`, 16) / 2;
    const layer = start_position >= 70 ? "01" : "00";
    const adjustedStartPosition = start_position >= 70 ? start_position - 70 : start_position;
    key_codes.forEach((keyCode, index) => {
      const key_position = getPositionByNumber(adjustedStartPosition + index, deviceProductId);
      if (key_position) {
        const [row, column] = key_position;
        const keyName = findNameByCode(`${keyCode[0]} ${keyCode[1]}`);
        updateKeycap(row, column, { label: keyName }, layer);
      }
    });
  } else {
    const start_position = parseInt(`${hexArray[1]}${hexArray[2]}`, 16) / 2;
    const layer = start_position >= 102 ? "01" : "00";
    const adjustedStartPosition = start_position >= 102 ? start_position - 102 : start_position;
    key_codes.forEach((keyCode, index) => {
      const key_position = getPositionByNumber(adjustedStartPosition + index, deviceProductId);
      if (key_position) {
        const [row, column] = key_position;
        const keyName = findNameByCode(`${keyCode[0]} ${keyCode[1]}`);
        updateKeycap(row, column, { label: keyName }, layer);
      }
    });
  }
}

export default InitKeyboardMapping;