import  './Keytest.css'

import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import KeyboardTest from './KeyboardTest.jsx';
import KeyTestLogs from './KeyTestLogs.jsx';
import GetKeyVoltage from './GetKeyVoltage';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { Tabs } from 'antd';

const KeyTest = ({deviceProductId}) => {
  const { t } = useTranslation();
  const [ flag, setFlag ] = useState('logs'); //控制按钮
  const SPECIAL_KEY_IDS = ['Tab', 'KeyW', 'KeyA', 'KeyS', 'KeyD', 'ShiftLeft', 'ControlLeft'];//专业校准的按键
  const UNSPECIAL_KEYS_IDS = ['MetaRight','Menu','F11','ScrLock','Pause'] ;  //浏览器识别不出来的按键
  const { dataQueue } = useHandleDevice();

  // 切换tab
  const changeTab= (value)=> {
    setFlag(value);
  }

  useEffect(() => {
    let intervalId;
    if (flag === 'fast' || flag === 'professional') {
      // 立即执行一次
      GetKeyVoltage(dataQueue);
      // 设置定时器，每100ms执行一次
      intervalId = setInterval(() => {
        GetKeyVoltage(dataQueue);
      }, 100);
    } else {
      clearInterval(intervalId);
    }

    // 清理函数
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [flag, dataQueue]);

  const getESeries = (aid) => {
    if ([9010,4097].includes(aid)) return 'ez80';
    if ([null,36869, 25344].includes(aid)) return 'ez63';
    if ([36880].includes(aid)) return 'ez60';
    return 'unknown';
  };
  const ESeries = getESeries(deviceProductId);
  var items = [];
  if([36880, 36869, 32773, 32784].includes(deviceProductId)) {
    items = [
      {
        key: 'logs',
        label: t('keytest_tab.logs')
      },
    ];
  } else {
    items = [
      {
        key: 'logs',
        label: t('keytest_tab.logs')
      },
      {
        key: 'professional',
        label: t('keytest_tab.professional')
      },
      {
        key: 'fast',
        label: t('keytest_tab.fast')
      },
    ];
  }

  return <>
    <div className="keytest-content" >
        <Tabs defaultActiveKey="logs" items={items}  onChange={changeTab} tabBarGutter={150} />
        { flag === 'logs' &&  <KeyTestLogs key="keytestlogs" /> }
        { flag === 'professional' && <KeyboardTest special_keys={SPECIAL_KEY_IDS} EZSeries={ESeries} itemPage = {flag}/> }
        { flag === 'fast' && <KeyboardTest UNSPECIAL_KEYS={UNSPECIAL_KEYS_IDS} need_calibration_times={2} EZSeries={ESeries} itemPage = {flag}/> }
    </div>
  </>
}
export default KeyTest;