
import { useTranslation } from 'react-i18next';
import keyTestDate from './keyTestDate.jsx';
import React, {useState, useEffect, useMemo, useContext} from 'react';
import KeyTestButton from './KeyTestButton.jsx';
import { KeyboardContext } from '../Keyboard/KeyboardContext.jsx';
import { Button, Alert, ConfigProvider } from 'antd';
import { InfoCircleFilled } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import GetTopBottomVoltage from '../KeyTest/GetTopBottomVoltage'

// SPECIAL_KEYS需要校准的按键，UNSPECIAL_KEYS不需要校准的按键, need_calibration_times需要校准的次数
function KeyboardTest({ special_keys=[], UNSPECIAL_KEYS=[], need_calibration_times=5, EZSeries, itemPage=''}) {
  const { t } = useTranslation();
  const { dataQueue, addToQueue } = useHandleDevice();
  const [buttonDisable, setButtonDisable] = useState(false)
  const { data, clearCalibrationKeys } = useContext(KeyboardContext);

  const keyboard_data = keyTestDate.filter(item=> item.ez_series && item.ez_series.includes(EZSeries));
  const keyboardWidth =  EZSeries === 'ez80' ? '942px' : '776px'
  const keyboardJsonPosition = EZSeries === 'ez80' ? 2 : EZSeries === 'ez63' ? 1 : EZSeries === 'ez60' ? 0 : 20

  let professionalKeys = []
  // 根据special_keys参数判断是专业校准模式还是快速校准模式
  // 专业校准模式: 只处理special_keys中指定的按键
  // 快速校准模式: 处理所有非空按键
  professionalKeys = keyboard_data
    .filter(item => {
      if (special_keys.length !== 0) {
        // 专业校准模式 - 只处理指定的特殊按键
        return special_keys.includes(item.key_value) && item.key_value !== '';
      } else {
        // 快速校准模式 - 处理所有非空按键
        return item.key_value && item.key_value !== '';
      }
    })
    .map(item => {
      // 为每个按键初始化校准次数和键名
      item.no = 0; // 初始化按键按下次数
      item.keyname = item.key_value; // 保存按键名称
      return item;
    });

  const [ keyPushs, setKeyPushs ] = useState(professionalKeys)
  const SPECIAL_KEY_IDS = professionalKeys.map(item=>item.keyname);

  const [pressStatus, setPressStatus] = useState('none')

  const handleStartButton = ()=>{
    setButtonDisable(true)
    clearCalibrationKeys()
    setPressStatus('start')
    addToQueue('4F')
  }

  const handleSaveButton = ()=>{
    setPressStatus('over');
    GetTopBottomVoltage(dataQueue);
    addToQueue('51')
  }

  useEffect(() => {
    if (buttonDisable) {
      // 检查所有特殊按键是否都达到了目标次数
      const allKeysComplete = keyPushs.every(keyPush => {
        if (!keyPush.row || !keyPush.col) return true; // Skip keys without position data

        const position = `${keyPush.row[keyboardJsonPosition]}-${keyPush.col[keyboardJsonPosition]}`;
        const count = data.calibrationKeys[position] || 0;

        return count >= need_calibration_times;
      });

      if (allKeysComplete) {
        setPressStatus('pressover');
      }
    }
  }, [buttonDisable, data.calibrationKeys]);

  return (
    <>
      <div className='keyboard_professional'>
        <div style={{width: '576px'}}>
          {/* <Steps current={stepNumber} items={steps} /> */}
        </div>
        <div className='keytestborad-professional-content'>
          <div className='keytest-keyborad-content'>
            <div className="keytest-keyborad-groups">
              <div className="keytest-keyborad-group" style={{ width: keyboardWidth }}>
                {keyboard_data.map((item) => {
                  const keyPushData = keyPushs.filter(k => k.keyname === item.key_value);
                  if (item.col) {
                    const Col = item.col //EZSeries 表示 ez60,ze63 ,ze80 如果是 60 取 Col[0]  63 Col[1] 80 Col[2]
                    const Row = item.row //EZSeries 表示 ez60,ze63 ,ze80 如果是 60 取 Row[0] 63 Row[1] 80 Row[2]
                    const keycap = data.keycaps["00"][`${Row[keyboardJsonPosition]}-${Col[keyboardJsonPosition]}`]
                    var realtime_voltage = keycap.realtimeVoltage
                    var max_voltage = keycap.maxKeyVoltage
                    var min_voltage = keycap.minKeyVoltage
                  } else {
                    var realtime_voltage = 0
                    var max_voltage = 0
                    var min_voltage = 0
                  }
                    return  <KeyTestButton
                      key={item.id}
                      item={item}
                      isSpecialKey={SPECIAL_KEY_IDS.includes(item.key_value)}
                      keyClickCount={data.calibrationKeys}
                      status={pressStatus}
                      special_icon_base_number={need_calibration_times}
                      realtime_voltage={realtime_voltage}
                      max_voltage={max_voltage}
                      min_voltage={min_voltage}
                      keyboardJsonPosition={keyboardJsonPosition}
                      onClick={() => console.log('Key pressed:', item.key_value)}
                    />
                })}
              </div>
            </div>
          </div>
          <div style={{height: '42px', width: '100%'}}>
            <ConfigProvider theme={{
              components: {
                Alert: {
                  contentHeight: 42,
                  colorInfoBg: '#196BE5',
                  colorInfo: '#FFFFFF'
                },
              },
            }}>
              {
                pressStatus === 'over' && (
                  <Alert
                    message={t('keytest_tab.content_alert_completed')}
                    type="info"
                    showIcon
                    closable
                    style={{width: '100%'}}
                  />
                )
              }
            </ConfigProvider>
          </div>
          <div className='keytest-keycap-button'>
            {pressStatus === 'none' &&
              <Button type="primary" onClick={handleStartButton}>{t('keytest_tab.start_calibration')}</Button>
            }
            {pressStatus === 'start' &&
              <Button type="primary" disabled>{t('keytest_tab.save_calibration')}</Button>
            }
            {pressStatus === 'pressover' &&
              <Button type="primary" onClick={handleSaveButton}>{t('keytest_tab.save_calibration')}</Button>
            }
            {pressStatus === 'over' &&
              <Button type="primary" disabled>{t('keytest_tab.save_calibration')}</Button>
            }
          </div>
        </div>
      </div>

      <div className='keyboard_professional-note'>
        <div className='keyboard_professional-content'>
          <div className='keyboard_professional-text'>
            <InfoCircleFilled style={{color: '#D7D7DB', fontSize: '15px'}} />
            <span>{t('keytest_tab.steps_content_title')}</span>
          </div>
          <div className='keyboard_professional-steps'>
            {itemPage === 'fast' &&
                <span>{t('keytest_tab.steps_content_fast_main')}
                </span> }
            {itemPage === 'professional' &&
                <span>{t('keytest_tab.steps_content_main')}
                </span> }
            <span className='text-steps-item'>{t('keytest_tab.steps_content_note1')}</span>
            {itemPage === 'professional' &&
            <span className='text-steps-item'>{t('keytest_tab.steps_content_note2')}</span>}
            {itemPage === 'fast' &&
            <span className='text-steps-item'>{t('keytest_tab.steps_content_fast_note2')}</span>}
            <span className='text-steps-item'>{t('keytest_tab.steps_content_note3')}</span>
          </div>
        </div>
      </div>
    </>
  );
}

export default KeyboardTest;