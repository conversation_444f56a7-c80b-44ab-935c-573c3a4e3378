import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

const Waveform = (prop) => {
  const { t } = useTranslation();
  const canvasRef = useRef(null);
  const keyPressTimes = useRef([]);
  const [apm, setApm] = useState();
  const animationId = useRef(null);
  const waveformData = useRef([]);
  const [apmreset,setApmreset] = useState()

  // 监听键盘事件
  useEffect(() => {
    const handleKeyPress = () => {
      const now = Date.now();
      keyPressTimes.current.push(now);
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // APM 计算逻辑
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      // 保留最近5秒内的按键事件
      keyPressTimes.current = keyPressTimes.current.filter(
        (time) => now - time < 5000
      );
      const currentApm = keyPressTimes.current.length * 12; // 5秒内次数 * 12 = 每分钟次数
      setApm(currentApm);
      waveformData.current.push(currentApm);
    }, 100); // 每100ms更新一次APM

    return () => clearInterval(interval);
  }, []);

  // 动画逻辑
  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    const drawWaveform = () => {
      ctx.clearRect(0, 0, width, height);

      // 保留最近100个数据点
      if (waveformData.current.length > 100) {
        waveformData.current = waveformData.current.slice(-100);
      }

      // 绘制波形
      ctx.beginPath();
      ctx.strokeStyle = '#196BE5';
      ctx.lineWidth = 2;

      waveformData.current.forEach((value, index) => {
        const x = (index / 100) * width;
        const y = height - (value / 500) * height; // 假设APM最大300

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();
      animationId.current = requestAnimationFrame(drawWaveform);
    };

    drawWaveform();

    return () => cancelAnimationFrame(animationId.current);
  }, []);

  return (
    <>
        <div className='operation-panel-apm'>
                    <div className='operation-panel-apm-label'>
                       <span className='operation-panel-apm-name'>APM</span>
                       <span className='operation-panel-apm-value'>{prop.apmreset === 'stop' ?  '0' : apm}</span>
                    </div>
                    <span>{t('keytest.operation-panel-apm')}</span>
        </div>
        <div className='operation-panel-apm-group'>
        <canvas
           ref={canvasRef}
           width={118}
           height={50}
           style={{ borderBottom: '2px solid #ffffff' }}
         />
        </div>
        </>
  );
};

export default Waveform;