import { createContext, useState, useContext } from 'react';

export const KeyboardContext = createContext({
  data: {
    menuItem: 'keymap',
    maxSelection: 1,
    keycaps: {}, // 格式: { "00": { "00-00": { label: "ESC" }, ... } }
    calibrationKeys: {},
    currentLayer: "00",
    currentSelectedKey: {},
    currentSelectedKeycaps: {},
    showAdvancedKey: false,
    showLayerSwitch: true,
    showSelectButton: false,
    showKeyboard: true
  },
  updateKeycap: () => {},
  setCurrentLayer: () => {},
  setCurrentSelectedKey: () => {},
  setMenuItem: () => {},
  setMaxSelection: () => {},
  setCurrentSelectedKeycaps: () => {},
  setShowAdvancedKey: () => {},
  setShowLayerSwitch: () => {},
  setShowSelectButton: () => {},
  setShowKeyboard: () => {},
  setCalibrationKeys: () => {}
});

export function KeyboardProvider({ children }) {
  const [keyboardData, setKeyboardData] = useState({
    keycaps: {
      "00": {}, // Layer 0
      "01": {}, // Layer 1
      "performance": {}
    },
    currentLayer: "00",
    maxSelection: 1,
    currentSelectedKey: {},
    currentSelectedKeycaps: {},
    menuItem: 'keymap',
    showAdvancedKey: false,
    showLayerSwitch: true,
    showSelectButton: false,
    showKeyboard: true,
    calibrationKeys: {}
  });

  const updateKeycap = (row, column, newData, layer = null) => {
    setKeyboardData(prev => ({
      ...prev,
      keycaps: {
        ...prev.keycaps,
        [layer || prev.currentLayer]: {
          ...prev.keycaps[layer || prev.currentLayer],
          [`${row}-${column}`]: {
            ...prev.keycaps[layer || prev.currentLayer]?.[`${row}-${column}`],
            ...newData
          }
        }
      }
    }));
  };

  const setCurrentLayer = (layer) => {
    setKeyboardData(prev => ({
      ...prev,
      currentLayer: layer
    }));
  };

  const setCurrentSelectedKey = (key) => {
    setKeyboardData(prev => ({
      ...prev,
      currentSelectedKey: key
    }));
  };

  const setCurrentSelectedKeycaps = (keycaps) => {
    setKeyboardData(prev => ({
      ...prev,
      currentSelectedKeycaps: keycaps
    }));
    if (keycaps.length > 0) {
      if (keycaps[0] && keycaps[0].label && keycaps[0].label.includes('<br/>')) {
        setKeyboardData(prev => ({
          ...prev,
          performance: {
            activeKey: 'rt'
          }
        }));
      } else if (keycaps[0] && keycaps[0].label && !keycaps[0].label.includes('<br/>')) {
        setKeyboardData(prev => ({
          ...prev,
          performance: {
            activeKey: 'basic'
          }
        }));
      }
    } else {
      setKeyboardData(prev => ({
        ...prev,
        performance: {
          activeKey: ''
        }
      }));
    }
  };

  const setShowAdvancedKey = (show) => {
    setKeyboardData(prev => ({
      ...prev,
      showAdvancedKey: show
    }));
  };

  const setMenuItem = (menuItem) => {
    setKeyboardData(prev => ({
      ...prev,
      menuItem: menuItem
    }));
  };

  const setMaxSelection = (max) => {
    setKeyboardData(prev => ({
      ...prev,
      maxSelection: max
    }));
  };

  const setShowLayerSwitch = (show) => {
    setKeyboardData(prev => ({
      ...prev,
      showLayerSwitch: show
    }));
  };

  const setShowSelectButton = (show) => {
    setKeyboardData(prev => ({
      ...prev,
      showSelectButton: show
    }));
  };

  const setShowKeyboard = (show) => {
    setKeyboardData(prev => ({
      ...prev,
      showKeyboard: show
    }));
  };

  const setCalibrationKeys = (key_position) => {
    setKeyboardData(prev => ({
      ...prev,
      calibrationKeys: {
        ...prev.calibrationKeys,
        [key_position]: (prev.calibrationKeys[key_position] || 0) + 1
      }
    }));
  };

  const clearCalibrationKeys = () => {
    setKeyboardData(prev => ({
      ...prev,
      calibrationKeys: {}
    }));
  }

  return (
    <KeyboardContext.Provider value={{
      data: keyboardData,
      updateKeycap,
      setKeyboardData,
      setCurrentLayer,
      setCurrentSelectedKey,
      setMaxSelection,
      setMenuItem,
      setCurrentSelectedKeycaps,
      setShowAdvancedKey,
      setShowLayerSwitch,
      setShowSelectButton,
      setShowKeyboard,
      setCalibrationKeys,
      clearCalibrationKeys
    }}>
      {children}
    </KeyboardContext.Provider>
  );
}

export function useKeyboard() {
  return useContext(KeyboardContext);
}