import { useContext, useState, useRef } from 'react';
import { KeyboardContext } from './KeyboardContext';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { Tabs, Button, Flex } from 'antd';
import ez80_background from '../../assets/ez80_background.png';
import { triggerFocus } from 'antd/es/input/Input';
import { useTranslation } from 'react-i18next';
import processString from './processString' ;


function Keyboard() {
  const { data, setCurrentLayer, setCurrentSelectedKey, setCurrentSelectedKeycaps } = useContext(KeyboardContext);
  const { advancedKey, setPerformance, performance, backlight, stableMode } = useHandleDevice();
  const activeKeys = data.currentSelectedKeycaps;
  const [isDragging, setIsDragging] = useState(triggerFocus);
  const { t } = useTranslation();

  const keyboardStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  };

  const containerStyle = {
    display: 'flex',
    padding: '10px',
    // background: '#0B0C0E',
    alignItems: 'center',
  };

  const layerSwitchStyle = {
    marginBottom: '1em',
    height: '46px' // 固定高度
  };

  const buttonGroupStyle = {
    marginBottom: '1em',
    marginLeft: '2em',
    display: 'flex',
    gap: '8px',
    flexWrap: 'wrap',
    justifyContent: 'center'
  };

  const rowStyle = {
    display: 'flex'
  };

  const firstRowStyle = {
    ...rowStyle,
    marginBottom: '2em'
  };

  const getKeyLabel = (row, column) => {
    let original_label = data.keycaps[data.currentLayer][`${row}-${column}`]?.label;
    if (original_label?.includes('<br/>')) {
      if (stableMode === '01') {
        let final_press_label, final_release_label;

        const [press, release] = original_label?.split('<br/>')
        if (stableMode === '01') {
          if (parseFloat(press) <= 0.01) {
            final_press_label = '0.005'
          } else {
            final_press_label = press
          }
          if (parseFloat(release) <= 0.01) {
            final_release_label = '0.005'
          } else {
            final_release_label = release
          }
        } else {
          final_press_label = press
          final_release_label = release
        }
        return `${final_press_label}<br/>${final_release_label}`
      } else {
        return original_label;
      }
    } else {
      return original_label;
    }
  };

  const getKeyAdvancedType = (row, column) => {
    return data.keycaps[data.currentLayer][`${row}-${column}`]?.advancedKeyType;
  };

  const handlePerformanceLayer = (keycaps) => {
    if (keycaps.length === 0) {
      setPerformance(prev => ({
        ...prev,
        activeKey: '',
        triggerPoint: 0,
        pressTriggerPoint: 0,
        releaseTriggerPoint: 0,
        bottomProtectionPoint: 0,
        switchType: '00'
      }));
      return;
    }

    const key = keycaps[0];
    if (key && key.label && key.label.includes('<br/>')) {
      // RT模式，解析标签中的参数
      const firstKey = data.keycaps[data.currentLayer][`${key.row}-${key.column}`]
      const triggerPoint = firstKey.rapidTrigger.triggerPoint || 0;
      const pressTriggerPoint = firstKey.rapidTrigger.pressTravel || 0;
      const releaseTriggerPoint = firstKey.rapidTrigger.releaseStroke || 0;
      const bottomProtectionPoint = firstKey.rapidTrigger.bottomProtectionTravel || 0;
      const switchType = firstKey.rapidTrigger.switchType || '00';
      setPerformance(prev => ({
        ...prev,
        activeKey: 'rt',
        triggerPoint,
        pressTriggerPoint,
        releaseTriggerPoint,
        bottomProtectionPoint,
        switchType
      }));
    } else {
      // 机械模式，只需要触发点
      const triggerPoint = key?.label ? parseFloat(key.label) || 0 : 0;
      const firstKey = data.keycaps[data.currentLayer][`${key.row}-${key.column}`]
      const switchType = firstKey.rapidTrigger.switchType || '00';

      setPerformance(prev => ({
        ...prev,
        activeKey: 'basic',
        triggerPoint,
        pressTriggerPoint: 0,
        releaseTriggerPoint: 0,
        bottomProtectionPoint: 0,
        switchType
      }));
    }
  }

  const handleMouseDown = (row, column, label) => {
    if (data.maxSelection === 0) return;

    setIsDragging(true);
    handleKeycapClick(row, column, label);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const handleMouseEnter = (row, column, label) => {
    if (!isDragging) return;

    // 鼠标滑动时只添加新的按键，不取消已选中的按键
    if (!Array.isArray(activeKeys) || !activeKeys.some(key => key.row === row && key.column === column)) {
      if (data.maxSelection > 0 && activeKeys.length >= data.maxSelection) {
        if (data.maxSelection === 1) {
          const newActiveKeys = [{ row, column, label }];
          setCurrentSelectedKey({ row, column, label });
          setCurrentSelectedKeycaps(newActiveKeys);
          if (data.currentLayer === 'performance') {
            handlePerformanceLayer(newActiveKeys);
          }
        }
        return;
      }

      const newActiveKeys = [...activeKeys, { row, column, label }];
      setCurrentSelectedKey({ row, column, label });
      setCurrentSelectedKeycaps(newActiveKeys);
      if (data.currentLayer === 'performance') {
        handlePerformanceLayer(newActiveKeys);
      }
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleKeycapClick = (row, column, label) => {
    if (data.maxSelection === 0) {
      return;
    }

    const keyPosition = { row, column, label };

    if (Array.isArray(activeKeys) && activeKeys.some(key => key.row === row && key.column === column)) {
      // 如果按键已激活，取消激活
      const newActiveKeys = activeKeys.filter(key => !(key.row === row && key.column === column));
      setCurrentSelectedKey({});
      setCurrentSelectedKeycaps(newActiveKeys);
      if (data.currentLayer === 'performance') {
        handlePerformanceLayer(newActiveKeys);
      }
    } else {
      // 如果按键未激活
      if (data.maxSelection > 0) {
        if (activeKeys.length >= data.maxSelection) {
          // 如果是单选模式
          if (data.maxSelection === 1) {
            const newActiveKeys = [keyPosition];
            setCurrentSelectedKey(keyPosition);
            setCurrentSelectedKeycaps(newActiveKeys);

            if (data.currentLayer === 'performance') {
              handlePerformanceLayer(newActiveKeys);
            }
          }
          return;
        }
      }

      const newActiveKeys = Array.isArray(activeKeys) ? [...activeKeys, keyPosition] : [keyPosition];
      setCurrentSelectedKey(keyPosition);
      setCurrentSelectedKeycaps(newActiveKeys);

      if (data.currentLayer === 'performance') {
        handlePerformanceLayer(newActiveKeys);
      }
    }
  }

  // 新增功能按钮处理函数
  const handleSelectWASD = () => {
    const wasdKeys = [
      { row: "02", column: "02", label: getKeyLabel("02", "02") },
      { row: "03", column: "01", label: getKeyLabel("03", "01") },
      { row: "03", column: "03", label: getKeyLabel("03", "03") },
      { row: "03", column: "02", label: getKeyLabel("03", "02") }
    ];
    setCurrentSelectedKeycaps(wasdKeys);
    if (data.currentLayer === 'performance') {
      handlePerformanceLayer(wasdKeys);
    }
  };

  const handleSelectAll = () => {
    const allKeys = [];
    // 遍历所有按键位置并添加到选中列表
    for (let row = 0; row <= 5; row++) {
      for (let col = 0; col <= 16; col++) {
        const rowHex = row.toString().padStart(2, '0');
        const colHex = col.toString(16).toUpperCase().padStart(2, '0');
        if (data.keycaps[data.currentLayer][`${rowHex}-${colHex}`]) {
          allKeys.push({
            row: rowHex,
            column: colHex,
            label: data.keycaps[data.currentLayer][`${rowHex}-${colHex}`].label
          });
        }
      }
    }
    setCurrentSelectedKeycaps(allKeys);
    handlePerformanceLayer(allKeys);
  };

  const handleInvertSelection = () => {
    const allKeys = [];
    const currentSelected = new Set(activeKeys.map(key => `${key.row}-${key.column}`));

    // 遍历所有按键，如果不在当前选中列表中则添加
    for (let row = 0; row <= 5; row++) {
      for (let col = 0; col <= 16; col++) {
        const rowHex = row.toString().padStart(2, '0');
        const colHex = col.toString(16).toUpperCase().padStart(2, '0');
        const keyId = `${rowHex}-${colHex}`;
        if (data.keycaps[data.currentLayer][keyId] && !currentSelected.has(keyId)) {
          allKeys.push({
            row: rowHex,
            column: colHex,
            label: data.keycaps[data.currentLayer][keyId].label
          });
        }
      }
    }
    setCurrentSelectedKeycaps(allKeys);
    handlePerformanceLayer(allKeys);
  };

  const handleClearSelection = () => {
    setCurrentSelectedKeycaps([]);
    if (data.currentLayer === 'performance') {
      handlePerformanceLayer([]);
    }
  };

  const renderKeycap = (props) => {
    const {
      row,
      column,
      size = 'u1',
      label,
      style = {},
      advancedKeyType = ''
    } = props;

    const keyId = `${data.currentLayer}-${row}-${column}`;
    const keyData = data.keycaps?.[keyId] || { label };
    const keyColor = data.keycaps?.[`${data.currentLayer}`][`${row}-${column}`]?.color;
    const isActive = Array.isArray(activeKeys) && activeKeys.some(key => key.row === row && key.column === column);
    let content = ""
    let newClass = false;

    if (advancedKeyType === 'rs' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>RS<br/><span style='color: #1668dc;'>一</span></div>"
    } else if (advancedKeyType === 'socd' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>SOCD<br/><span style='color: #1668dc;'>一</span></div>"
    } else if (advancedKeyType === 'mt' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>MT<br/><span style='color: #1668dc;'>一</span></div>"
    } else if (advancedKeyType === 'dks' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>DKS<br/><span style='color: #1668dc;'>一</span></div>"
    } else if (advancedKeyType === 'tgl' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>TGL<br/><span style='color: #1668dc;'>一</span></div>"
    } else {
      content = keyData.label || label
    }



    // 把按键这块的进行更新其他的保持不变
    if (data.menuItem === "keymap"){
      // 将所有的键和值进行对比图片显示
      let contentdo = "";
      contentdo = processString(content,'picture')
      let id = contentdo+'-iconid'
      content =  `<div class='keyicon_class' id='${id}'>
         <svg
        aria-hidden="true"
        width=42
        height=42
        >
        <use href= #icon-${contentdo=== '' ? 'default' : contentdo} />
      </svg>
      </div>`
    }else if(['socd','mt','dks','rs','tgl'].includes(advancedKeyType) &&  data.showAdvancedKey){
      //  特殊按键不处理
    }else{
      // 统一的进行数字后面的特殊的显示进行去掉
      content = processString(content,'text')
    }

    if (data.menuItem === "light" && backlight.mode === 46){
      content = `<div style='margin-top: 0px;text-align: center;width: 100%;'>
      <div style='height: 24px;display:flex;align-items:center;justify-content:center;' class='keycap-text'>${content}</div>
      <div style='background-color: ${keyColor};height: 3px;width: calc(100% - 2em);margin-top: 10px;margin-left: 1em;'></div></div>`
    }

    if (data.menuItem === 'performance'){
      let original_label = data.keycaps['00'][`${row}-${column}`]?.label;
      const contentn = processString(original_label,'text')
      content = ` <span class="keycap-inside-up">${contentn}</span> <span
          class=" keycap-inside-dou"> ${content}</span>`
      newClass = true

    }

    return (
      <div
        className={`keycap  ${ data.menuItem === 'performance'  ? 'keycap-dou': ''}  keycap-${size} ${isActive ? 'active' : ''} ${(advancedKeyType === 'socd' || advancedKeyType === 'mt' || advancedKeyType === 'dks' || advancedKeyType === 'rs' || advancedKeyType === 'tgl') && data.showAdvancedKey ? 'advanced-key' : ''}`}
        onMouseDown={() => handleMouseDown(row, column, label)}
        onMouseEnter={() => handleMouseEnter(row, column, label)}
        onMouseUp={handleMouseUp}
        style={style}
      >
        <span
          className={newClass ? 'keycap-inside keycap-dou-in' : 'keycap-inside'}
          dangerouslySetInnerHTML={{
            __html: content
          }}
        />
      </div>
    );
  }

  return (
    <div style={keyboardStyle}>
      <div style={containerStyle}>
        <div style={layerSwitchStyle}>
          {data.showLayerSwitch && (
            <Tabs
              tabPosition='left'
              activeKey={data.currentLayer}
              onChange={setCurrentLayer}
              items={[
                {
                  key: "00",
                  label: t('keyboard.main_layer'),
                },
                {
                  key: "01",
                  label: t('keyboard.secondly_layer'),
                }
              ]}
            />
          )}
        </div>
        <div style={{
          backgroundImage: `url(${ez80_background})`,
          backgroundSize: "100% 100%",
          backgroundPosition: "center",
          width: "1168px",
          height: "460px",
          paddingTop: "56px",
          paddingLeft: "35px"
        }}
          onMouseLeave={handleMouseLeave}>
          <style>
          {`
            .keycap-u6_25 {
              width: calc(6.42 * var(--keycap-width));
            }

            .keycap-u2_75 {
              width: calc(2.85 * var(--keycap-width));
            }

            .keycap-u2_25 {
              width: calc(2.34 * var(--keycap-width));
            }

            .keycap-u1_25 {
              width: calc(1.293 * var(--keycap-width));
            }
          `}
        </style>
          <div className='firstRowStyle80'>
          {/* <div style={firstRowStyle}> */}
            {renderKeycap({ row: "00", column: "00", label: getKeyLabel("00", "00"), advancedKeyType: getKeyAdvancedType("00", "00"), style: {marginLeft: '0.25em'} })}
            {renderKeycap({ row: "00", column: "01", label: getKeyLabel("00", "01"), advancedKeyType: getKeyAdvancedType("00", "01"), style: {marginLeft: '3.62em'} })}
            {renderKeycap({ row: "00", column: "02", label: getKeyLabel("00", "02"), advancedKeyType: getKeyAdvancedType("00", "02") })}
            {renderKeycap({ row: "00", column: "03", label: getKeyLabel("00", "03"), advancedKeyType: getKeyAdvancedType("00", "03") })}
            {renderKeycap({ row: "00", column: "04", label: getKeyLabel("00", "04"), advancedKeyType: getKeyAdvancedType("00", "04") })}
            {renderKeycap({ row: "00", column: "05", label: getKeyLabel("00", "05"), advancedKeyType: getKeyAdvancedType("00", "05"), style: {marginLeft: '2.45em'} })}
            {renderKeycap({ row: "00", column: "06", label: getKeyLabel("00", "06"), advancedKeyType: getKeyAdvancedType("00", "06") })}
            {renderKeycap({ row: "00", column: "07", label: getKeyLabel("00", "07"), advancedKeyType: getKeyAdvancedType("00", "07") })}
            {renderKeycap({ row: "00", column: "08", label: getKeyLabel("00", "08"), advancedKeyType: getKeyAdvancedType("00", "08") })}
            {renderKeycap({ row: "00", column: "09", label: getKeyLabel("00", "09"), advancedKeyType: getKeyAdvancedType("00", "09"), style: {marginLeft: '2.5em'} })}
            {renderKeycap({ row: "00", column: "0A", label: getKeyLabel("00", "0A"), advancedKeyType: getKeyAdvancedType("00", "0A") })}
            {renderKeycap({ row: "00", column: "0B", label: getKeyLabel("00", "0B"), advancedKeyType: getKeyAdvancedType("00", "0B") })}
            {renderKeycap({ row: "00", column: "0C", label: getKeyLabel("00", "0C"), advancedKeyType: getKeyAdvancedType("00", "0C") })}
            {renderKeycap({ row: "00", column: "0D", label: getKeyLabel("00", "0D"), advancedKeyType: getKeyAdvancedType("00", "0D"), style: {marginLeft: '1.5em'} })}
            {renderKeycap({ row: "00", column: "0E", label: getKeyLabel("00", "0E"), advancedKeyType: getKeyAdvancedType("00", "0E") })}
            {renderKeycap({ row: "00", column: "0F", label: getKeyLabel("00", "0F"), advancedKeyType: getKeyAdvancedType("00", "0F") })}
          </div>

          <div style={{...rowStyle}}>
            {renderKeycap({ row: "01", column: "00", label: getKeyLabel("01", "00"), advancedKeyType: getKeyAdvancedType("01", "00") })}
            {renderKeycap({ row: "01", column: "01", label: getKeyLabel("01", "01"), advancedKeyType: getKeyAdvancedType("01", "01") })}
            {renderKeycap({ row: "01", column: "02", label: getKeyLabel("01", "02"), advancedKeyType: getKeyAdvancedType("01", "02") })}
            {renderKeycap({ row: "01", column: "03", label: getKeyLabel("01", "03"), advancedKeyType: getKeyAdvancedType("01", "03") })}
            {renderKeycap({ row: "01", column: "04", label: getKeyLabel("01", "04"), advancedKeyType: getKeyAdvancedType("01", "04") })}
            {renderKeycap({ row: "01", column: "05", label: getKeyLabel("01", "05"), advancedKeyType: getKeyAdvancedType("01", "05") })}
            {renderKeycap({ row: "01", column: "06", label: getKeyLabel("01", "06"), advancedKeyType: getKeyAdvancedType("01", "06") })}
            {renderKeycap({ row: "01", column: "07", label: getKeyLabel("01", "07"), advancedKeyType: getKeyAdvancedType("01", "07") })}
            {renderKeycap({ row: "01", column: "08", label: getKeyLabel("01", "08"), advancedKeyType: getKeyAdvancedType("01", "08") })}
            {renderKeycap({ row: "01", column: "09", label: getKeyLabel("01", "09"), advancedKeyType: getKeyAdvancedType("01", "09") })}
            {renderKeycap({ row: "01", column: "0A", label: getKeyLabel("01", "0A"), advancedKeyType: getKeyAdvancedType("01", "0A") })}
            {renderKeycap({ row: "01", column: "0B", label: getKeyLabel("01", "0B"), advancedKeyType: getKeyAdvancedType("01", "0B") })}
            {renderKeycap({ row: "01", column: "0C", label: getKeyLabel("01", "0C"), advancedKeyType: getKeyAdvancedType("01", "0C") })}
            {renderKeycap({ row: "01", column: "0D", label: getKeyLabel("01", "0D"), size: "u2", advancedKeyType: getKeyAdvancedType("01", "0D") })}
            {renderKeycap({ row: "01", column: "0E", label: getKeyLabel("01", "0E"), style: {marginLeft: '1.35em'}, advancedKeyType: getKeyAdvancedType("01", "0E") })}
            {renderKeycap({ row: "01", column: "0F", label: getKeyLabel("01", "0F"), advancedKeyType: getKeyAdvancedType("01", "0F") })}
            {renderKeycap({ row: "01", column: "10", label: getKeyLabel("01", "10"), advancedKeyType: getKeyAdvancedType("01", "10") })}
          </div>

          <div style={rowStyle}>
            {renderKeycap({ row: "02", column: "00", label: getKeyLabel("02", "00"), size: "u1_5", advancedKeyType: getKeyAdvancedType("02", "00") })}
            {renderKeycap({ row: "02", column: "01", label: getKeyLabel("02", "01"), advancedKeyType: getKeyAdvancedType("02", "01") })}
            {renderKeycap({ row: "02", column: "02", label: getKeyLabel("02", "02"), advancedKeyType: getKeyAdvancedType("02", "02") })}
            {renderKeycap({ row: "02", column: "03", label: getKeyLabel("02", "03"), advancedKeyType: getKeyAdvancedType("02", "03") })}
            {renderKeycap({ row: "02", column: "04", label: getKeyLabel("02", "04"), advancedKeyType: getKeyAdvancedType("02", "04") })}
            {renderKeycap({ row: "02", column: "05", label: getKeyLabel("02", "05"), advancedKeyType: getKeyAdvancedType("02", "05") })}
            {renderKeycap({ row: "02", column: "06", label: getKeyLabel("02", "06"), advancedKeyType: getKeyAdvancedType("02", "06") })}
            {renderKeycap({ row: "02", column: "07", label: getKeyLabel("02", "07"), advancedKeyType: getKeyAdvancedType("02", "07") })}
            {renderKeycap({ row: "02", column: "08", label: getKeyLabel("02", "08"), advancedKeyType: getKeyAdvancedType("02", "08") })}
            {renderKeycap({ row: "02", column: "09", label: getKeyLabel("02", "09"), advancedKeyType: getKeyAdvancedType("02", "09") })}
            {renderKeycap({ row: "02", column: "0A", label: getKeyLabel("02", "0A"), advancedKeyType: getKeyAdvancedType("02", "0A") })}
            {renderKeycap({ row: "02", column: "0B", label: getKeyLabel("02", "0B"), advancedKeyType: getKeyAdvancedType("02", "0B") })}
            {renderKeycap({ row: "02", column: "0C", label: getKeyLabel("02", "0C"), advancedKeyType: getKeyAdvancedType("02", "0C") })}
            {renderKeycap({ row: "02", column: "0D", label: getKeyLabel("02", "0D"), size: "u1_5", advancedKeyType: getKeyAdvancedType("02", "0D") })}
            {renderKeycap({ row: "02", column: "0E", label: getKeyLabel("02", "0E"), style: {marginLeft: '1.35em'}, advancedKeyType: getKeyAdvancedType("02", "0E") })}
            {renderKeycap({ row: "02", column: "0F", label: getKeyLabel("02", "0F"), advancedKeyType: getKeyAdvancedType("02", "0F") })}
            {renderKeycap({ row: "02", column: "10", label: getKeyLabel("02", "10"), advancedKeyType: getKeyAdvancedType("02", "10") })}
          </div>

          <div style={rowStyle}>
            {renderKeycap({ row: "03", column: "00", label: getKeyLabel("03", "00"), size: "u1_75", advancedKeyType: getKeyAdvancedType("03", "00") })}
            {renderKeycap({ row: "03", column: "01", label: getKeyLabel("03", "01"), advancedKeyType: getKeyAdvancedType("03", "01") })}
            {renderKeycap({ row: "03", column: "02", label: getKeyLabel("03", "02"), advancedKeyType: getKeyAdvancedType("03", "02") })}
            {renderKeycap({ row: "03", column: "03", label: getKeyLabel("03", "03"), advancedKeyType: getKeyAdvancedType("03", "03") })}
            {renderKeycap({ row: "03", column: "04", label: getKeyLabel("03", "04"), advancedKeyType: getKeyAdvancedType("03", "04") })}
            {renderKeycap({ row: "03", column: "05", label: getKeyLabel("03", "05"), advancedKeyType: getKeyAdvancedType("03", "05") })}
            {renderKeycap({ row: "03", column: "06", label: getKeyLabel("03", "06"), advancedKeyType: getKeyAdvancedType("03", "06") })}
            {renderKeycap({ row: "03", column: "07", label: getKeyLabel("03", "07"), advancedKeyType: getKeyAdvancedType("03", "07") })}
            {renderKeycap({ row: "03", column: "08", label: getKeyLabel("03", "08"), advancedKeyType: getKeyAdvancedType("03", "08") })}
            {renderKeycap({ row: "03", column: "09", label: getKeyLabel("03", "09"), advancedKeyType: getKeyAdvancedType("03", "09") })}
            {renderKeycap({ row: "03", column: "0A", label: getKeyLabel("03", "0A"), advancedKeyType: getKeyAdvancedType("03", "0A") })}
            {renderKeycap({ row: "03", column: "0B", label: getKeyLabel("03", "0B"), advancedKeyType: getKeyAdvancedType("03", "0B") })}
            {renderKeycap({ row: "03", column: "0C", label: getKeyLabel("03", "0C"), size: "u2_25", advancedKeyType: getKeyAdvancedType("03", "0C") })}
          </div>

          <div style={rowStyle}>
            {renderKeycap({ row: "04", column: "00", label: getKeyLabel("04", "00"), size: "u2_25", advancedKeyType: getKeyAdvancedType("04", "00") })}
            {renderKeycap({ row: "04", column: "01", label: getKeyLabel("04", "01"), advancedKeyType: getKeyAdvancedType("04", "01") })}
            {renderKeycap({ row: "04", column: "02", label: getKeyLabel("04", "02"), advancedKeyType: getKeyAdvancedType("04", "02") })}
            {renderKeycap({ row: "04", column: "03", label: getKeyLabel("04", "03"), advancedKeyType: getKeyAdvancedType("04", "03") })}
            {renderKeycap({ row: "04", column: "04", label: getKeyLabel("04", "04"), advancedKeyType: getKeyAdvancedType("04", "04") })}
            {renderKeycap({ row: "04", column: "05", label: getKeyLabel("04", "05"), advancedKeyType: getKeyAdvancedType("04", "05") })}
            {renderKeycap({ row: "04", column: "06", label: getKeyLabel("04", "06"), advancedKeyType: getKeyAdvancedType("04", "06") })}
            {renderKeycap({ row: "04", column: "07", label: getKeyLabel("04", "07"), advancedKeyType: getKeyAdvancedType("04", "07") })}
            {renderKeycap({ row: "04", column: "08", label: getKeyLabel("04", "08"), advancedKeyType: getKeyAdvancedType("04", "08") })}
            {renderKeycap({ row: "04", column: "09", label: getKeyLabel("04", "09"), advancedKeyType: getKeyAdvancedType("04", "09") })}
            {renderKeycap({ row: "04", column: "0A", label: getKeyLabel("04", "0A"), advancedKeyType: getKeyAdvancedType("04", "0A") })}
            {renderKeycap({ row: "04", column: "0B", label: getKeyLabel("04", "0B"), size: "u2_75", advancedKeyType: getKeyAdvancedType("04", "0B") })}
            {renderKeycap({ row: "04", column: "0C", label: getKeyLabel("04", "0C"), style: {marginLeft: '5.56em'}, advancedKeyType: getKeyAdvancedType("04", "0C") })}
          </div>

          <div style={rowStyle}>
            {renderKeycap({ row: "05", column: "00", label: getKeyLabel("05", "00"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "00") })}
            {renderKeycap({ row: "05", column: "01", label: getKeyLabel("05", "01"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "01") })}
            {renderKeycap({ row: "05", column: "02", label: getKeyLabel("05", "02"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "02") })}
            {renderKeycap({ row: "05", column: "03", label: getKeyLabel("05", "03"), size: "u6_25", advancedKeyType: getKeyAdvancedType("05", "03") })}
            {renderKeycap({ row: "05", column: "04", label: getKeyLabel("05", "04"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "04") })}
            {renderKeycap({ row: "05", column: "05", label: getKeyLabel("05", "05"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "05") })}
            {renderKeycap({ row: "05", column: "06", label: getKeyLabel("05", "06"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "06") })}
            {renderKeycap({ row: "05", column: "07", label: getKeyLabel("05", "07"), size: "u1_25", advancedKeyType: getKeyAdvancedType("05", "07") })}
            {renderKeycap({ row: "05", column: "08", label: getKeyLabel("05", "08"), style: {marginLeft: '1.3em'}, advancedKeyType: getKeyAdvancedType("05", "08") })}
            {renderKeycap({ row: "05", column: "09", label: getKeyLabel("05", "09"), advancedKeyType: getKeyAdvancedType("05", "09") })}
            {renderKeycap({ row: "05", column: "0A", label: getKeyLabel("05", "0A"), advancedKeyType: getKeyAdvancedType("05", "0A") })}
          </div>
        </div>
        <div style={buttonGroupStyle}>
          {data.showSelectButton && (
            <Flex gap="middle" vertical>
              <Button onClick={handleSelectWASD} style={{marginBottom: '1em', borderColor: 'transparent', color: '#A3A3A3', fontFamily: 'Anton'}}>WASD</Button>
              <Button onClick={handleSelectAll} type='default' style={{backgroundColor:'#151619', borderColor: 'transparent', color: '#A3A3A3'}} autoInsertSpace={false}>{t('keyboard.select_all')}</Button>
              <Button onClick={handleInvertSelection} type='default' style={{backgroundColor:'#151619', borderColor: 'transparent', color: '#A3A3A3'}} autoInsertSpace={false}>{t('keyboard.invert_selection')}</Button>
              <Button onClick={handleClearSelection} type='default' style={{backgroundColor:'#151619', borderColor: 'transparent', color: '#A3A3A3'}} autoInsertSpace={false}>{t('keyboard.clear_selection')}</Button>
            </Flex>
          )}
        </div>
      </div>
    </div>
  );
}

export default Keyboard;