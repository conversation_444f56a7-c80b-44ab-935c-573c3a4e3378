import { hsToRgb, rgbToHex } from '../../utils/colorCoversion';
import { getPositionByNumber } from '../../utils/hidUtils';

const SetLight = (hexArray, setBacklight, updateKeycap, deviceProductId) => {
  if (hexArray[0] === '08') {
    if (hexArray[2] === '01') {
      const fourthHex = hexArray[3];
      const fourthDecimal = parseInt(fourthHex, 16);
      setBacklight(prev => ({ ...prev, brightness: fourthDecimal }));
    } else if (hexArray[2] === '02') {
      const fourthHex = hexArray[3];
      const fourthDecimal = parseInt(fourthHex, 16);
      setBacklight(prev => ({ ...prev, mode: fourthDecimal }));
    } else if (hexArray[2] === '03') {
      const fourthHex = hexArray[3];
      const fourthDecimal = parseInt(fourthHex, 16);
      setBacklight(prev => ({ ...prev, speed: fourthDecimal }));
    } else if (hexArray[2] === '04') {
      const fourthHex = hexArray[3];
      const fifthHex = hexArray[4];
      const fourthDecimal = parseInt(fourthHex, 16);
      const fifthDecimal = parseInt(fifthHex, 16);
      const rgb_arr = hsToRgb(fourthDecimal, fifthDecimal);
      setBacklight(prev => ({ ...prev, color: rgb_arr }));
    }
  } else if (hexArray[0] === '36') {
    const key_colors = [
      [hexArray[4], hexArray[5]],
      [hexArray[6], hexArray[7]],
      [hexArray[8], hexArray[9]],
      [hexArray[10], hexArray[11]],
      [hexArray[12], hexArray[13]],
      [hexArray[14], hexArray[15]],
      [hexArray[16], hexArray[17]],
      [hexArray[18], hexArray[19]],
      [hexArray[20], hexArray[21]],
      [hexArray[22], hexArray[23]],
      [hexArray[24], hexArray[25]],
      [hexArray[26], hexArray[27]],
      [hexArray[28], hexArray[29]],
      [hexArray[30], hexArray[31]]
    ];

    if (deviceProductId === 25344) {
      const start_position = parseInt(`${hexArray[1]}${hexArray[2]}`, 16) / 2;
      const layer = start_position >= 70 ? "01" : "00";
      const adjustedStartPosition = start_position >= 70 ? start_position - 70 : start_position;
      key_colors.forEach((keyColor, index) => {
        const key_position = getPositionByNumber(adjustedStartPosition + index, deviceProductId);
        if (key_position) {
          const [row, column] = key_position;
          const keyColorRgb = hsToRgb(parseInt(keyColor[0], 16), parseInt(keyColor[1], 16));
          updateKeycap(row, column, { color: rgbToHex(keyColorRgb[0], keyColorRgb[1], keyColorRgb[2]) }, layer);
        }
      });
    } else {
      const start_position = parseInt(`${hexArray[1]}${hexArray[2]}`, 16) / 2;
      const layer = start_position >= 102 ? "01" : "00";
      const adjustedStartPosition = start_position >= 102 ? start_position - 102 : start_position;
      key_colors.forEach((keyColor, index) => {
        const key_position = getPositionByNumber(adjustedStartPosition + index, deviceProductId);
        if (key_position) {
          const [row, column] = key_position;
          const keyColorRgb = hsToRgb(parseInt(keyColor[0], 16), parseInt(keyColor[1], 16));
          updateKeycap(row, column, { color: rgbToHex(keyColorRgb[0], keyColorRgb[1], keyColorRgb[2]) }, layer);
        }
      });
    }
  } else if (hexArray[0] === '37') {
    const key_color = [hexArray[3], hexArray[4]];
    const keyColorRgb = hsToRgb(parseInt(key_color[0], 16), parseInt(key_color[1], 16));
    updateKeycap(hexArray[1], hexArray[2], { color: rgbToHex(keyColorRgb[0], keyColorRgb[1], keyColorRgb[2]) }, '00');
  }
};

export default SetLight;
