import { Dropdown, Button, Space, Tooltip } from 'antd';
import { DownOutlined, LockOutlined } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import GetAllInfo from '../HIDDevice/GetAllInfo';
import { useKeyboard } from '../Keyboard/KeyboardContext';
import { useTranslation } from 'react-i18next';
import { SettingOutlined, AimOutlined, DesktopOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import './Profile.css';

const Profile = () => {
  const { t } = useTranslation();
  const items = [
    {
      label: (
        <div className="profile-item-container">
          <div style={{display: 'flex', alignItems: 'center'}}>
            <SettingOutlined style={{fontSize: '18px'}} />
            <div style={{marginLeft: '16px', fontSize: '18px'}}>
              {t('profile.custom_profile')}
            </div>
          </div>
          <div className="profile-item" style={{marginLeft: '226px'}}>
            Fn + I
          </div>
        </div>
      ),
      key: '00',
    },
    {
      label: (
        <div className="profile-item-container">
          <div style={{display: 'flex', alignItems: 'center'}}>
            <DesktopOutlined style={{fontSize: '18px'}} />
            <div style={{marginLeft: '16px', fontSize: '18px'}}>
              {t('profile.office_profile')}
            </div>
          </div>
          <Tooltip title={t('profile.profile_tooltip')} placement="bottom">
            <QuestionCircleOutlined style={{marginLeft: '4px'}} />
          </Tooltip>
          <LockOutlined style={{marginLeft: '4px'}} />
          <div className="profile-item" style={{marginLeft: '226px'}}>
            Fn + O
          </div>
        </div>
      ),
      key: '01',
    },
    {
      label: (
        <div className="profile-item-container">
          <div style={{display: 'flex', alignItems: 'center'}}>
            <AimOutlined style={{fontSize: '18px'}} />
            <div style={{marginLeft: '16px', fontSize: '18px'}}>
              {t('profile.esport_profile')}
            </div>
          </div>
          <Tooltip title={t('profile.profile_tooltip')} placement="bottom">
            <QuestionCircleOutlined style={{marginLeft: '4px'}} />
          </Tooltip>
          <LockOutlined style={{marginLeft: '4px'}} />
          <div className="profile-item" style={{marginLeft: '226px'}}>
            Fn + P
          </div>
        </div>
      ),
      key: '02',
    }
  ];

  const { profile, setProfile, addToQueue, dataQueue } = useHandleDevice();
  const { setKeyboardData } = useKeyboard();

  const handleMenuClick = (e) => {
    setProfile(e.key);
    setKeyboardData(prev => ({
      ...prev,
      keycaps: {
        ...prev.keycaps,
        "00": {},
        "01": {}
      }
    }));
    addToQueue(`40 ${e.key} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
    GetAllInfo(dataQueue);
    addToQueue("2E 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00")
    addToQueue("1E 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00")
    addToQueue("22 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00")
    addToQueue("2A 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00")
  };

  const menuProps = {
    items,
    selectable: true,
    defaultSelectedKeys: [profile],
    onClick: handleMenuClick,
  };

  return (
    <div style={{marginTop: '10px'}}>
      <Dropdown menu={menuProps} placement="bottom">
        <Button style={{width: '474px', height: '48px'}}>
          <Space style={{width: '100%', justifyContent: 'space-between', padding: '0 10px', color: '#D7D7DB66'}}>
            {(() => {
              const item = items.find(item => item.key === profile);
              const labelContent = item.label.props.children[0];
              return labelContent;
            })()}
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    </div>
  )
}

export default Profile;