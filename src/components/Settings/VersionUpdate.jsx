import { useState, useEffect } from 'react';
import { Modal, Button, Progress, Spin, Result, Tag } from 'antd';
import { changeToHighLowHex } from '../../utils/hidUtils';
import HIDDevice from '../HIDDevice/HIDDevice';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useTranslation } from 'react-i18next';

const VersionUpdate = () => {
  const [updating, setUpdating] = useState(false);
  const { firmwareVersion, addToQueue, setUpdatingFirmware, deviceProductId, send_data,
    updateModalVisible, setUpdateModalVisible, forceUpdate, setForceUpdate, downloadUrl, latestVersion, updateLog } = useHandleDevice();
  const [currentStep, setCurrentStep] = useState(1);
  const [updatePercent, setUpdatePercent] = useState(0);
  const { t, i18n } = useTranslation();
  useEffect(() => {
    if ([4660, 25345, 29953].includes(deviceProductId)) {
      setUpdateModalVisible(true);
      setUpdating(true)
      setUpdatePercent(0)
      setTimeout(() => {
        setCurrentStep(3);
        setTimeout(() => {
          setCurrentStep(4);

          // 添加进度条更新逻辑
          let progress = 0;
          const interval = setInterval(() => {
            progress += 100/150; // 15秒内完成，每100ms更新一次
            if (progress > 60 && progress < 61) {
              setTimeout(async () => {
                const checkVersion = await fetch(`${import.meta.env.VITE_API_URL}/api/firmware_check/firmware_check?device_id=${deviceProductId}&deploy_env=${import.meta.env.VITE_API_ENV}`);
                const checkVersionData = await checkVersion.json();
                const response = await fetch(checkVersionData.download_url);
                const firmwareBlob = await response.blob();
                const firmwareArrayBuffer = await firmwareBlob.arrayBuffer();
                const firmware = new Uint8Array(firmwareArrayBuffer);
                let firmware_block_hex = changeToHighLowHex(firmware.length / 512)
                const baseStr = `AA 55 55 AA ${firmware_block_hex}`;
                const currentBytes = baseStr.split(' ').length;
                const paddingBytes = 64 - currentBytes;
                const padding = Array(paddingBytes).fill('00').join(' ');
                send_data(`${baseStr} ${padding}`);

                // 将固件分成多个数据包发送
                const PACKET_SIZE = 64; // HID包大小
                for(let i = 0; i < firmware.length; i += PACKET_SIZE) {
                  const packet = firmware.slice(i, i + PACKET_SIZE);
                  // 添加包头标识
                  const header = new Uint8Array([]); // 假设0x08是固件更新的命令
                  const fullPacket = new Uint8Array([...header, ...packet]);
                  send_data(Array.from(fullPacket).map(b => b.toString(16).padStart(2, '0')).join(' '));
                }
              }, 2000);
            }
            if(progress >= 100) {
              clearInterval(interval);
              progress = 100;
              setTimeout(() => {
                setCurrentStep(5);
                setTimeout(() => {
                  Modal.success({
                    title: t('settings.version_update.firmware_update_success')
                  });
                  setUpdating(false);
                  setUpdateModalVisible(false);
                }, 2000);
              }, 500);
            }
            setUpdatePercent(Math.min(Math.round(progress), 100));
          }, 100);
        }, 2000);
      }, 2000);
    }
  }, [deviceProductId]);

  const handleUpdate = async () => {
    try {
      setUpdatingFirmware(true);
      addToQueue('07 00 02 01')
      setUpdating(true);
      setTimeout(() => {
        setCurrentStep(2);
      }, 2000);
    } catch (error) {
      Modal.error({
        title: t('settings.version_update.firmware_update_failed'),
        content: t('settings.version_update.please_check_device_connection_and_try_again')
      });
    }
  };

  const handleCancel = () => {
    if (!forceUpdate) {
      setUpdateModalVisible(false);
    }
  };

  return (
    <div>
      <Modal
        open={updateModalVisible}
        onOk={handleUpdate}
        okText={t('settings.version_update.update_now')}
        confirmLoading={updating}
        closable={forceUpdate ? false : !updating}
        onCancel={forceUpdate ? undefined : handleCancel}
        cancelButtonProps={{ style: { display: forceUpdate ? 'none' : undefined } }}
        cancelText={t('settings.version_update.update_later')}
        footer={updating ? null : undefined}
        keyboard={forceUpdate ? false : true}
        maskClosable={forceUpdate ? false : true}
        styles={{ mask: { backdropFilter: 'blur(10px)' }}}
      >
        {!updating && (
          <div style={{color: '#EFF0F5', opacity: '0.85'}}>
            <h3>{t('settings.version_update.find_new_version')} {latestVersion} <Tag color="#196be5" style={{borderRadius: '20px', marginLeft: '8px'}}>New</Tag></h3>
            <p>{t('settings.version_update.update_content')}</p>
            <div style={{opacity: '0.65'}}>
              {i18n.language === 'zh-CN' && <div dangerouslySetInnerHTML={{ __html: updateLog?.description_zh?.replace(/\r\n/g, '<br/>') }} />}
              {i18n.language === 'zh-TW' && <div dangerouslySetInnerHTML={{ __html: updateLog?.description_tw?.replace(/\r\n/g, '<br/>') }} />}
              {i18n.language === 'en-US' && <div dangerouslySetInnerHTML={{ __html: updateLog?.description_en?.replace(/\r\n/g, '<br/>') }} />}
              {i18n.language === 'ja-JP' && <div dangerouslySetInnerHTML={{ __html: updateLog?.description_ja?.replace(/\r\n/g, '<br/>') }} />}
              {i18n.language === 'ko-KR' && <div dangerouslySetInnerHTML={{ __html: updateLog?.description_ko?.replace(/\r\n/g, '<br/>') }} />}
            </div>
          </div>
        )}
        {updating && (
          <div>
            <p>{t('settings.version_update.please_do_not_disconnect_the_device_during_the_update_process')}</p>
            {currentStep === 1 && (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                <div>{t('settings.version_update.step_1_of_6')}</div>
                <div>{t('settings.version_update.waiting_for_update_mode_connection')}</div>
                <div style={{marginTop: '16px', width: '100%', textAlign: 'center'}}>
                  <Spin />
                </div>
              </div>
            )}
            {currentStep === 2 && (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                <div>{t('settings.version_update.step_2_of_6')}</div>
                <div>{t('settings.version_update.allow_browser_to_download_firmware_update')}</div>
                <div style={{marginTop: '16px', width: '100%', textAlign: 'center'}}>
                  <HIDDevice />
                </div>
              </div>
            )}
            {currentStep === 3 && (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                <div>{t('settings.version_update.step_3_of_6')}</div>
                <div>{t('settings.version_update.get_firmware_update')}</div>
                <div style={{marginTop: '16px', width: '100%', textAlign: 'center'}}>
                  <Spin />
                </div>
              </div>
            )}
            {currentStep === 4 && (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                <div>{t('settings.version_update.step_4_of_6')}</div>
                <div>{t('settings.version_update.flash_firmware_to_device')}</div>
                <div style={{marginTop: '16px', width: '100%', textAlign: 'center'}}>
                  <Progress percent={updatePercent} status="active" />
                </div>
              </div>
            )}
            {currentStep === 5 && (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                <div>{t('settings.version_update.step_5_of_6')}</div>
                <div>{t('settings.version_update.waiting_for_device_connection')}</div>
                <div style={{marginTop: '16px', width: '100%', textAlign: 'center'}}>
                  <Spin />
                </div>
              </div>
            )}
            {currentStep === 6 && (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                <div>{t('settings.version_update.step_6_of_6')}</div>
                <Result
                  status="success"
                  title={t('settings.version_update.firmware_update_success')}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default VersionUpdate;