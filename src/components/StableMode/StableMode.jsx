import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useTranslation } from 'react-i18next';
import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown, Space, message, Modal, Progress } from 'antd';
import { useState, useEffect } from 'react';

const StableMode = () => {
  const [messageApi, contextHolderMsg] = message.useMessage();
  const { stableMode, setStableMode, addToQueue, deviceProductId } = useHandleDevice();
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [countdown, setCountdown] = useState(15);
  const [progressPercent, setProgressPercent] = useState(100);

  var allItems = []
  if ([36880, 36869, 32773, 32784].includes(deviceProductId)) {
    allItems = [
      {
        label: t('performance.tournament_mode'),
        key: 'tournament_mode',
        value: "tournament_mode",
        extra: '0.1mmRT',
      },
      {
        label: t('performance.esports_mode'),
        key: 'esports_mode',
        value: "esports_mode",
        extra: '0.05mmRT',
      },
      {
        label: t('performance.beserk_mode'),
        key: 'beserk_mode',
        value: "beserk_mode",
        extra: '0.01mmRT',
      }
    ];
  } else {
    allItems = [
      {
        label: t('performance.tournament_mode'),
        key: 'tournament_mode',
        value: "tournament_mode",
        extra: '0.1mmRT',
      },
      {
        label: t('performance.esports_mode'),
        key: 'esports_mode',
        value: "esports_mode",
        extra: '0.05mmRT',
      },
      {
        label: t('performance.beserk_mode'),
        key: 'beserk_mode',
        value: "beserk_mode",
        extra: '0.01mmRT',
      },
      {
        label: t('performance.limitless_mode'),
        key: 'limitless_mode',
        value: "limitless_mode",
        extra: '0.005mmRT',
      },
    ];
  }

  // const getFilteredItems = () => {
  //   return allItems.filter(item => {
  //     const currentMode = stableMode === "00" ? "beserk_mode" :
  //                        stableMode === "01" ? "limitless_mode" :
  //                        stableMode === "02" ? "tournament_mode" :
  //                        "esports_mode";
  //     return item.key !== currentMode;
  //   });
  // };

  useEffect(() => {
    let timer;
    if (isModalOpen) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 0) {
            clearInterval(timer);
            setIsModalOpen(false);
            return 15;
          }
          return prevCountdown - 0.1;
        });
        setProgressPercent((countdown / 15) * 100);
      }, 100);
    }
    return () => clearInterval(timer);
  }, [isModalOpen, countdown]);

  const handleStableMode = (e) => {
    if (stableMode !== "01"  && e.key === "limitless_mode") {
      addToQueue(`46 01`)
      setStableMode('01')
      setIsModalOpen(true);
      setCountdown(15);
      setProgressPercent(100);
    } else if ( e.key === "tournament_mode") {
      setStableMode('02')
      addToQueue(`46 02`)
      messageApi.success(t('performance.switch_tournament_mode_tip'));
    } else if ( e.key === "esports_mode") {
      setStableMode('03')
      addToQueue(`46 03`)
      messageApi.success(t('performance.switch_esports_mode_tip'));
    } else if ( e.key === "beserk_mode") {
      setStableMode('00')
      addToQueue(`46 00`)
      messageApi.success(t('performance.switch_beserk_mode_tip'));
    }
  }

  const menuProps = {
    items: allItems,
    onClick: handleStableMode
  };

  return (
    <>
    {contextHolderMsg}
    <Dropdown menu={menuProps} placement="bottom">
      <Button>
        <Space>
          {stableMode === "00" ? t('performance.beserk_mode') : stableMode === "01" ? t('performance.limitless_mode') : stableMode === "02" ? t('performance.tournament_mode') : t('performance.esports_mode')}
          <DownOutlined />
        </Space>
      </Button>
    </Dropdown>
    <Modal
      centered
      open={isModalOpen}
      closable={false}
      footer={null}
      width={800}
    >
      <div style={{fontSize: '22px', margin: '20px 30px'}}>{t('performance.messages.warm_tips')}</div>
      <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', margin: '20px 30px'}}>
        <p style={{fontSize: '20px'}}>{t('performance.messages.warm_tips_crazy_mode')}</p>
      </div>
      <Progress
        percent={progressPercent}
        size="small"
        showInfo={false}
      />
    </Modal>
    </>
  );
};

export default StableMode;