{"meta": {"title": "IQUNIX EZ"}, "language": "한국어", "play_your_way_with_ez": "EZ로 마음껏 즐기세요!", "effortlessly_conlgure_your_ez": "EZ를 손쉽게 설정하고, 독특한 설정을 탐색하며, 원하는 대로 기기를 맞춤화하세요.", "effortlessly_conlgure_your_ez2": "쉬운 접근, 쉬운 설정, 쉬운 저장 - 쉽게 승리하세요!", "get_start": "시작", "cancel": "취소", "confirm": "확인", "save": "저장", "next_step": "다음 단계", "finish": "완료", "home": {"initializing": "초기화 중...", "find_device": "장치 발견", "device_initialized": "장치 초기화 완료", "device_loaded": "성공적으로 로드됨", "find_device_tip": "장치 찾기"}, "sidebar": {"keymap": "키", "light": "조명", "performance": "성능", "advanced_key": "유동 키", "career_preset": "직업프리셋", "keytest": "키 캘리브레이션", "settings": "설정"}, "keytest_tab": {"logs": "키 테스트", "professional": "프로 FPS 캘리브레이션", "fast": "빠른 캘리브레이션", "content_alert_completed": "캘리브레이션 완료", "start_calibration": "캘리브레이션 시작", "save_calibration": "캘리브레이션 저장", "steps_content_title": "키보드를 분리하거나 스위치를 교체하거나 펌웨어를 업데이트한 후에는 수동으로 캘리브레이션을 수행하여 키 전압을 정상으로 유지하고 키 차터링이나 연결 문제를 방지하세요.", "steps_content_fast_main": "빠른 캘리브레이션 단계：", "steps_content_main": "프로 FPS 캘리브레이션 단계：", "steps_content_note1": "1. 【캘리브레이션 시작】 버튼을 클릭합니다", "steps_content_note2": "2. W/A/S/D/Tab 키, 왼쪽 Shift 키, 왼쪽 Ctrl 키를 완전히 5번 눌러 위 원 내의 숫자가 사라질 때까지 반복합니다", "steps_content_fast_note2": "2. 키보드의 모든 키를 완전히 2번 눌러 위 원 내의 숫자가 사라질 때까지 반복합니다", "steps_content_note3": "3. 【캘리브레이션 저장】 버튼을 클릭하여 캘리브레이션을 완료합니다"}, "keyboard": {"select_all": "전체선택", "invert_selection": "반전선택", "clear_selection": "지우기", "main_layer": "메인레이어", "secondly_layer": "첫번째레이어"}, "keymap": {"basic": "기본 레이아웃", "light": "조명 제어", "multimedia": "미디어 버튼", "mouse": "마우스 기능", "other": "고급 기능", "layer": "레이어", "clicktitle": "키맵", "press_sec_switch": "5초 동안 누르고 프로필을 전환", "click_cut_layer": "키를 눌러 레이어를 전환", "Fclick_title": "F 키", "system_title": "Windows 기능", "system_title_osx": "macOS 기능", "click_quick_switch": "프로 키를 켜/끄려면 탭하세요.", "syskey": {"system_com_close": "전원 끄기", "system_commac_close": "전원 끄기", "system_com_sleep": "절전 모드", "system_com_wake": "깨우기", "system_com_search": "검색", "system_com_calc": "계산기 열기", "system_com_mycom": "파일 탐색기 열기", "system_win_lock": "윈도우 키 잠금 기능", "system_com_mail": "이메일 열기"}, "otherkey": {"click_quick_socd": "SOCD"}, "layerkey": {"layer_mode_a1": "사용자 프로필", "layer_mode_a2": "사무실 프로필", "layer_mode_a3": "e스포츠 프로필", "layer_mode_mo0": "메인 레이어", "layer_mode_mo1": "Fn 레이어 1"}, "lightkey": {"lamp_effect_switch": "RGB 토글", "lamp_bright_up": "밝기 +", "lamp_bright_down": "밝기 -", "lamp_speed_up": "속도 +", "lamp_speed_down": "속도 -", "lamp_effect_mode": "RGB 모드", "lamp_active_direction": "이동 방향 전환", "mult_color_cycling": "색상 사이클링"}, "multimediakey": {"mult_music": "미디어 플레이어", "mult_next": "다음 트랙", "mult_nomu": "음소거", "mult_play": "재생/일시 정지\t", "mult_up": "이전 트랙", "mult_voice_down": "볼륨 -", "mult_voice_up": "볼륨 +"}, "mousekey": {"mouse_left": "왼쪽 클릭", "mouse_right": "오른쪽 클릭", "mouse_sliod_click": "휠 클릭", "mouse_d_left": "왼쪽 더블 클릭", "mouse_d_right": "오른쪽 더블 클릭", "mouse_up_siod": "위로 스크롤", "mouse_down_sliod": "아래로 스크롤", "mouse_l_sliod": "왼쪽으로 스크롤", "mouse_r_sliod": "오른쪽으로 스크롤"}, "basic_key": {"blank_key": "공백 키", "letter_key": "주 키 영역 - 문자", "number_key": "주 키 영역 - 숫자", "function_key": "주 키 영역 - 기호 및 기능 키", "f_key": "F 영역", "f_key_extend": "확장 F 영역", "modifier_key": "수정 키", "function_key_extend": "기능 영역", "shift_symbol": "Shift 기호"}, "other_key": {"lock_key": "잠금", "advanced_key": "고급 키", "accuracy_layer": "정확도 레이어", "socd_switch": "SOCD 스위치"}, "layer_key": {"switch_layer": "레이어 전환"}}, "light": {"light_preset": "조명 효과 프리셋", "global_brightness": "전체 밝기", "global_speed": "전역 속도", "light_mode": {"0": "사용 금지", "46": "사용자 정의", "1": "전체 정적", "2": "주 키 구역 + 기능 키 구역", "3": "상하 그라디언트 효과", "4": "좌우 그라디언트 효과", "5": "숨쉬기 효과", "6": "S-컬러 밴드", "7": "V-컬러 밴드", "8": "S-회전 풍차", "9": "V-회전 풍차", "10": "나선형 (채도)", "11": "나선형 (밝기)", "12": "풀 컬러 사이클", "13": "좌우 사이클", "14": "상하 사이클", "15": "무지개 이동 V자형", "16": "안쪽/바깥쪽 사이클", "17": "양방향 안쪽/바깥쪽 사이클", "18": "풍차 사이클", "19": "나선형 사이클", "20": "양방향 신호등", "21": "무지개 신호등", "22": "무지개 풍차", "23": "빗방울", "24": "무지개 빗방울", "25": "색조 숨쉬기 효과", "26": "색조 흔들림", "27": "색조 파동", "28": "픽셀 비", "29": "픽셀 흐름", "30": "픽셀 프랙탈", "31": "타이핑 열지도", "32": "숫자 비", "33": "단색 반응", "34": "단색 반응", "35": "넓은 단색 반응", "36": "다색 넓은 반응", "37": "교차 단색 반응", "38": "다색 교차 반응", "39": "노드 단색 반응", "40": "다색 노드 반응", "41": "스플래시", "42": "다색 스플래시", "43": "단색 스플래시", "44": "다색 단색 스플래시"}}, "performance": {"please_select_key": "키를 먼저 선택하세요", "axis_selection": "Switch 선택", "performance_setting": "성능 설정", "loading_text_1": "정확도를 실시간으로 보정 중입니다. 잠시 기다려 주세요.", "loading_text_2": "승리를 위한 준비 중입니다. 잠시만 기다려 주세요.", "xingguicizhou": "Star Trail", "ciyu_gaming": "Magnetic Jade Gaming", "ciyu": "Magnetic Jade", "ciyu_pro": "Magnetic Jade Pro", "ciyu_max": "Magnetic Jade Max", "shenmi_x": "X Magnetic Switch", "trigger_point": "Actuation Point", "quick_trigger_mode": "Rapid Trigger", "quick_trigger_mode_tip": "누름 활성화 및 해제를 위한 RT 민감도 설정", "press_trigger_point": "눌러서 활성화", "release_trigger_point": "떼어서 비활성화", "bottom_safe_area": "하단 안전 영역", "messages": {"use_warning": "경고", "use_warning_bottom_safe_area": "자석 스위치에 공차가 있으며 게임 중 손의 미세한 떨림이 있기 때문에, 데드존을 0으로 설정하면 게임 안정성에 영향을 미칠 수 있습니다. 0.005mm 빠른 트리거는 0 안전 구역을 지원하며, EZ의 자체 개발 기술을 전시하고 데이터 정확성을 보장합니다. 최상의 게임 경험을 위해 토너먼트 모드를 사용하는 것이 좋습니다.", "warm_tips": "팁", "warm_tips_crazy_mode": "0.005mm는 인간의 감지할 수 없는 거리입니다. 무극 모드에서는 0.005mm의 빠른 트리거를 지원하며, EZ 성능을 주로 보여주지만 게임 경험을 향상시키지 않습니다."}, "tournament_mode": "토너먼트 모드", "esports_mode": "e스포츠 모드", "beserk_mode": "광폭 모드", "limitless_mode": "무극 모드", "switch_tournament_mode_tip": "토너먼트 모드로 전환됨", "switch_esports_mode_tip": "e스포츠 모드로 전환됨", "switch_beserk_mode_tip": "광폭 모드로 전환됨", "switch_limitless_mode_tip": "무극 모드로 전환됨", "popover_content": {"quick_trigger_mode_content": "자신에게 맞는 촉감으로 작동 지점을 설정하세요. 낮을수록 좋지만, 실수로 눌릴 가능성이 높아집니다.", "release_trigger_point_content": "빠른 트리거의 수치는 손이 쉽게 떼어지지 않는 한 이론상 값이 낮을수록 좋습니다. 트리거가 되지 않는 문제가 발생하면 \"누르기(다시 트리거)\" 값 높여주세요."}}, "advanced_key": {"advanced_key_management": "바인딩 관리", "advanced_key_management_list": "키 바인딩", "advanced_key_settings": "바인딩 설정", "new_advanced_key": "새로 만들기", "new_advanced_key_tip": "새 매핑 생성", "list": {"rs_tip": "Rappy Snappy는 선택된 두 키를 모니터링하고, 더 깊이 눌린 키를 활성화합니다.", "socd_tip": "SOCD는 선택한 두 키를 모니터링하고, 설정에 따라 키를 활성화합니다.", "mt_tip": "하나의 키로 두 가지 기능을 수행합니다: 누르고 있으면 하나, 클릭하면 다른 하나.", "dks_tip": "하나의 키로 4가지 기능을 구현합니다: 4가지 다른 압력 수준에 따라 1~4개의 기능을 바인딩할 수 있습니다.", "tgl_tip": "키를 탭하여 연속 트리거를 켜거나 끌 수 있습니다. 키를 누르고 있으면 정상 트리거 동작이 됩니다."}, "rs": {"advanced_keyrs_title": "<PERSON><PERSON>", "advanced_keyrs_content": "키보드나 위의 미리보기에서 2개의 키를 선택하여 빠른 스냅을 할당합니다. 이 모드는 선택한 두 키를 모니터링하고, 더 눌린 키를 트리거합니다. 두 키가 모두 완전히 눌리면 두 키 모두 활성화됩니다."}, "socd": {"choose_two_keys": "두 개의 키 선택", "advanced_keyrs_title": "Snap Tap (SOCD)", "advanced_keyrs_content": "키보드나 위의 미리보기에서 2개의 키를 선택하여 SOCD에 할당합니다. 이 모드는 선택한 두 키를 모니터링하고 설정에 따라 활성화합니다.", "advanced_socdmodes_title": "스냅 탭 (SOCD) 설정", "advanced_socdmodes_content": "두 키가 동시에 눌렸을 때의 키 반응을 선택하세요.", "advanced_socdmodes_alert": "더 자연스러운 느낌을 위해 두 키가 동시에 눌려서 바닥에 닿을 때 두 키가 모두 활성화됩니다.", "choose_key_1": "키 1 선택", "choose_key_2": "키 2 선택", "choose_related_function": "해당 기능 선택", "back_cover": "마지막 입력 우선순위", "mutual_exclusion": "상호 배제 취소", "priority": "우선", "key_1": "키 1", "key_2": "키 2", "choose": "선택"}, "mt": {"mt_config": "MT 설정", "current_key": "현재 키", "choose_keyboard": "키 선택", "choose_key": "키 선택", "hold_key": "누르고", "single_click": "클릭", "hold_time_tip": "눌림 시간을 수동으로 조정합니다(기본값 200ms)"}, "dks": {"dks_config": "동적 키 스트로크 DKS", "choose_keyboard": "키 선택", "choose_key": "키 선택", "choose_key_1": "키 1 선택", "hold_key": "누르고", "advanced_modes_content": "팝업 창에서 바인딩할 키를 선택합니다. 단일 트리거를 추가하려면 플러스 아이콘을 클릭하고, 연속 트리거를 추가하려면 아이콘을 드래그합니다.", "single_click": "클릭", "press_start": "눌림 시작", "press_bottom": "바닥까지 눌림", "bottom_release": "바닥에서 해제", "release_end": "완전히 해제", "key_1": "키 위치 1", "key_2": "키 위치 2", "key_3": "키 위치 3", "key_4": "키 위치 4", "advanced_keyrs_title": "Dynamic Keystroke (DKS)", "advanced_keyrs_content": "키보드나 위의 미리보기에서 하나의 키를 선택하여 DKS에 바인딩합니다. 이 모드는 키의 누름 단계를 기준으로 최대 4개의 동작을 바인딩할 수 있습니다: 시작, 바닥, 바닥에서 놓기, 완전한 놓기. 설정할 키를 선택하십시오.", "first_and_fourth_dks_action_execution_point": "누름 시작과 완전 해제의 Actuation point", "second_and_third_dks_action_execution_point": "하단 눌림과 하단 떼기의 Actuation point"}, "tgl": {"tgl_config": "Toggle Key"}}, "career_preset": {"description": "재구성 중입니다. 기대해 주세요.", "apply_settings": "애플리케이션 설정", "apply_settings_success": "적용 완료", "career_info": {"tenz_desc": "캐나다 출신의 발로란트 프로 선수. 전 CSGO 프로 선수로, 정밀한 에임과 부드러운 움직임으로 유명하다. VCT 마스터즈 레이캬비크에서 북미 팀의 우승에 중요한 역할을 했다.", "prx_desc": "싱가포르 출신의 발로란트 프로 선수. APAC 팀을 이끌고 VCT 퍼시픽 챔피언십 우승과 VCT 챔피언스 준우승을 달성했다.", "lev_desc": "브라질 출신의 발로란트 프로 선수. 뛰어난 기량으로 세계 최고의 선수 중 한 명으로 널리 인정받고 있다. 과거 남미 최고의 팀에서 활동하며 몇 년 전 챔피언스 토너먼트 우승 경험이 있다.", "nrg_desc": "북미 출신의 발로란트 프로 선수. 다양한 역할 수행과 뛰어난 스킬 사용으로 주목받으며, 몇 년 전 VCT 마스터즈 우승에 기여했다.", "tarik_desc": "미국 출신의 발로란트 스트리머이자 전 CS 세계 챔피언. 현재는 인기 콘텐츠 크리에이터이자 대회 진행자로서 게임 이해도와 재미있는 해설로 사랑받고 있다.", "fnc_desc": "러시아 출신의 발로란트 프로 선수. 베를린 마스터즈에서 우승하며 역사를 만들었다."}}, "upgrade_version": {"firmware_update": "펌웨어 업데이트", "upgrade_version_tip": "최신 펌웨어로 업데이트 후 정상적으로 접근 가능합니다.", "step_1": "1단계:", "step_1_desc": "최신 펌웨어 다운로드", "download": "다운로드", "firmware": "펌웨어", "step_2": "2단계:", "step_2_desc": "키보드를 DFU 모드로 전환", "click_to_enter_dfu": "DFU 모드로 들어가기 클릭", "step_3": "3단계:", "open": "열기", "my_computer": "내 컴퓨터", "double_click": "더블 클릭하여 열기", "disk_name": "디스크 IQUNIX_DFU", "copy_or_drag": ", 다운로드한 펌웨어 파일을 해당 디스크에 복사하거나 드래그하여 넣기", "video_guide": "비디오 튜토리얼", "update_completed": "업데이트 완료"}, "keytest": {"operation-panel-apm": "분당 키 입력 횟수", "reset_pushs": "리셋", "browser_prompt_content": "브라우저 API 제한으로 인해 일부 키는 테스트할 수 없을 수 있습니다."}, "settings": {"enter_firmware_upgrade_mode": "업그레이드 시작", "restore_factory_settings": "공장 초기화", "firmware_version_info": "펌웨어 버전 정보", "current_firmware_version": "현재 버전:", "online_upgrade_firmware": "온라인 펌웨어 업그레이드", "manual_upgrade_firmware": "수동 펌웨어 업그레이드", "usage_instructions": "사용 설명서", "user_guide": "사용자 가이드", "other_settings": "기타 설정", "check_for_updates": "업데이트 확인", "new_version_available": "새 버전이 있습니다", "version_check": "버전 확인", "current_version_is_the_latest": "현재 버전은 최신 버전입니다", "check_for_updates_failed": "업데이트 확인 실패", "please_try_again_later": "나중에 다시 시도하십시오", "current_version": "현재 버전", "latest_version": "최신 버전", "update_log": "업데이트 로그", "download_url": "다운로드 URL", "force_update": "강제 업데이트", "update_modal_content": "펌웨어를 업데이트해야 합니까?", "update_modal_cancel": "지금 업데이트하지 않음", "update_modal_update": "지금 업데이트", "version_update": {"find_new_version": "새 버전이 발견되었습니다", "update_content": "업데이트 내용：", "please_do_not_disconnect_the_device_during_the_update_process": "업데이트 중에 장치 연결을 끊지 마세요", "step_1_of_6": "Step 1 of 6", "waiting_for_update_mode_connection": "업데이트 모드 연결을 기다리는 중", "step_2_of_6": "Step 2 of 6", "allow_browser_to_download_firmware_update": "브라우저에서 펌웨어 업데이트 다운로드 허용", "connect_device": "장치 연결", "step_3_of_6": "Step 3 of 6", "get_firmware_update": "펌웨어 업데이트를 받기", "step_4_of_6": "Step 4 of 6", "flash_firmware_to_device": "장치에 펌웨어 플래시", "step_5_of_6": "Step 5 of 6", "waiting_for_device_connection": "장치 연결을 기다리는 중", "firmware_update_success": "펌웨어 업데이트 성공", "step_6_of_6": "Step 6 of 6", "firmware_update_failed": "펌웨어 업데이트 실패", "please_check_device_connection_and_try_again": "장치 연결을 확인하고 다시 시도하세요", "update_now": "지금 업데이트", "update_later": "나중에 업데이트", "update_modal_title": "펌웨어 업데이트"}}, "profile": {"custom_profile": "사용자 정의 프로파일", "office_profile": "오피스 프로파일", "esport_profile": "e스포츠 프로파일", "profile_tooltip": "이 모드에서는 빠른 트리거 및 키 매핑 설정을 지원하지 않습니다"}}