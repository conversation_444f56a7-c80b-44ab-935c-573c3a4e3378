import KeyCodeMap from "../utils/KeyCodeMap";

// 辅助函数
export const parseHex = (hex) => parseInt(hex.replace(/\s+/g, ''), 16);

export const changeToHex = (decimal_value) =>
  Number(decimal_value).toString(16).padStart(2, '0');

export const changeToHighLowHex = (decimal_value) => {
  const num = Number(decimal_value);
  if (isNaN(num) || num < 0 || num > 0xFFFF) {
    throw new Error("请输入 0 到 65535 (0xFFFF) 范围内的有效数字");
  }

  const high = (num >> 8) & 0xFF;
  const low = num & 0xFF;
  const highHex = high.toString(16).padStart(2, '0').toUpperCase();
  const lowHex = low.toString(16).padStart(2, '0').toUpperCase();

  return `${highHex} ${lowHex}`;
};

export const getPositionByNumber = (number, deviceProductId) => {
  if (number < 272) {
    var rowIndex, colIndex;
    var row_codes = []
    // 配列
    if (deviceProductId === 25344) {
      var rows = [14, 14, 14, 14, 14]
    } else {
      var rows = [17, 17, 17, 17, 17, 17]
    }
    for (rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      for (colIndex = 0; colIndex < rows[rowIndex]; colIndex++) {
        // 根据行数和列数生成编码
        let rowHex = rowIndex.toString(16).padStart(2, '0').toUpperCase();
        let colHex = colIndex.toString(16).padStart(2, '0').toUpperCase();
        row_codes.push([rowHex, colHex]);
      }
    }

    return row_codes[number];
  } else {
    return null;
  }
}

export const findNameByCode = (code) => {
  for (let key in KeyCodeMap) {
    if (KeyCodeMap[key].code === code) {
      return KeyCodeMap[key].name;  // 返回对应的 name
    }
  }
  return "default";  // 如果找不到匹配的编码，返回默认值
}

export const findCodeByKey = (key) => {
  return KeyCodeMap[key].code;
}

export const parseFirmwareVersion = (lengthHex, versionHex) => {
  const length = parseInt(lengthHex, 16); // 将长度从16进制转为整数
  const versionBytes = versionHex.split(" ").slice(0, length); // 根据长度截取版本号字节
  const version = versionBytes
    .map((byte) => String.fromCharCode(parseInt(byte, 16))) // 转换每个字节为字符
    .join(""); // 拼接成字符串
  return version;
};

export const binaryToHex = (binaryString) => {
  const decimal = parseInt(binaryString, 2);
  return decimal.toString(16).toUpperCase().padStart(2, '0');
}

export const hexToBinary = (hexString) => {
  const decimal = parseInt(hexString, 16);
  return decimal.toString(2).padStart(8, '0');
}