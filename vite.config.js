import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { i18nAlly } from 'vite-plugin-i18n-ally'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), i18nAlly(),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), 'public/keyboard/icons')], // 指定 SVG 目录
      symbolId: 'icon-[name]',  // 定义 symbolId 格式
    }),
  ],
  assetsInclude: ['**/*.uf2'],
})
